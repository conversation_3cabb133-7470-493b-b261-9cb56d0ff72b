import XCTest
import AVFoundation
@testable import AudioEngine
@testable import MachineProtocols

// Import test utilities and mocks

final class AudioEngineTests: XCTestCase {

    var audioEngine: AudioEngineManager!

    override func setUp() {
        super.setUp()
        audioEngine = AudioEngineManager.shared
        // Reset engine to clean state
        audioEngine.reset()
    }

    override func tearDown() {
        // Clean up after each test
        audioEngine.reset()
        super.tearDown()
    }

    // MARK: - Basic Functionality Tests

    func testAudioEngineExists() throws {
        XCTAssertNotNil(audioEngine, "AudioEngine should exist")
        XCTAssertEqual(audioEngine.status, .uninitialized, "Initial status should be uninitialized")
    }

    func testSingletonPattern() throws {
        let engine1 = AudioEngineManager.shared
        let engine2 = AudioEngineManager.shared
        XCTAssertTrue(engine1 === engine2, "AudioEngineManager should be a singleton")
    }

    func testInitialState() throws {
        XCTAssertEqual(audioEngine.status, .uninitialized)
        XCTAssertNil(audioEngine.configuration)
        XCTAssertNil(audioEngine.lastError)
        XCTAssertFalse(audioEngine.isRunning)
    }

    // MARK: - Configuration Tests

    func testDefaultConfiguration() throws {
        let config = AudioEngineConfiguration()
        XCTAssertEqual(config.sampleRate, 44100.0)
        XCTAssertEqual(config.bufferSize, 512)
        XCTAssertEqual(config.channelCount, 2)
        #if os(iOS)
        XCTAssertEqual(config.sessionCategory, .playAndRecord)
        #endif
        XCTAssertTrue(config.enablePerformanceMonitoring)
        XCTAssertTrue(config.enableErrorRecovery)
    }

    func testCustomConfiguration() throws {
        #if os(iOS)
        let config = AudioEngineConfiguration(
            sampleRate: 48000.0,
            bufferSize: 256,
            channelCount: 1,
            sessionCategory: .playback,
            enablePerformanceMonitoring: false,
            enableErrorRecovery: false
        )
        XCTAssertEqual(config.sessionCategory, .playback)
        #else
        let config = AudioEngineConfiguration(
            sampleRate: 48000.0,
            bufferSize: 256,
            channelCount: 1,
            enablePerformanceMonitoring: false,
            enableErrorRecovery: false
        )
        #endif

        XCTAssertEqual(config.sampleRate, 48000.0)
        XCTAssertEqual(config.bufferSize, 256)
        XCTAssertEqual(config.channelCount, 1)
        XCTAssertFalse(config.enablePerformanceMonitoring)
        XCTAssertFalse(config.enableErrorRecovery)
    }

    // MARK: - Initialization Tests

    func testInitialization() throws {
        let config = AudioEngineConfiguration()

        try audioEngine.initialize(configuration: config)

        XCTAssertEqual(audioEngine.status, .ready)
        XCTAssertNotNil(audioEngine.configuration)
        XCTAssertEqual(audioEngine.configuration?.sampleRate, config.sampleRate)
        XCTAssertEqual(audioEngine.configuration?.bufferSize, config.bufferSize)
    }

    func testDoubleInitializationFails() throws {
        let config = AudioEngineConfiguration()

        try audioEngine.initialize(configuration: config)
        XCTAssertEqual(audioEngine.status, .ready)

        // Second initialization should fail
        XCTAssertThrowsError(try audioEngine.initialize(configuration: config)) { error in
            XCTAssertTrue(error is AudioEngineError)
            if case AudioEngineError.initializationFailed(let message) = error {
                XCTAssertTrue(message.contains("already initialized"))
            } else {
                XCTFail("Expected initializationFailed error")
            }
        }
    }

    // MARK: - Lifecycle Tests

    func testStartStop() throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        // Test start
        try audioEngine.start()
        XCTAssertEqual(audioEngine.status, .running)
        XCTAssertTrue(audioEngine.isRunning)

        // Test stop
        try audioEngine.stop()
        XCTAssertEqual(audioEngine.status, .stopped)
        XCTAssertFalse(audioEngine.isRunning)
    }

    func testStartWithoutInitializationFails() throws {
        XCTAssertThrowsError(try audioEngine.start()) { error in
            XCTAssertTrue(error is AudioEngineError)
            if case AudioEngineError.engineStartFailed = error {
                // Expected error type
            } else {
                XCTFail("Expected engineStartFailed error")
            }
        }
    }

    func testStopWithoutRunningFails() throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        XCTAssertThrowsError(try audioEngine.stop()) { error in
            XCTAssertTrue(error is AudioEngineError)
            if case AudioEngineError.engineStopFailed = error {
                // Expected error type
            } else {
                XCTFail("Expected engineStopFailed error")
            }
        }
    }

    // MARK: - Suspend/Resume Tests

    func testSuspendResume() throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)
        try audioEngine.start()

        // Test suspend
        try audioEngine.suspend()
        XCTAssertEqual(audioEngine.status, .suspended)

        // Test resume
        try audioEngine.resume()
        XCTAssertEqual(audioEngine.status, .running)
    }

    func testSuspendWithoutRunningFails() throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        XCTAssertThrowsError(try audioEngine.suspend()) { error in
            XCTAssertTrue(error is AudioEngineError)
            if case AudioEngineError.interruptionError = error {
                // Expected error type
            } else {
                XCTFail("Expected interruptionError")
            }
        }
    }

    func testResumeWithoutSuspendFails() throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)
        try audioEngine.start()

        XCTAssertThrowsError(try audioEngine.resume()) { error in
            XCTAssertTrue(error is AudioEngineError)
            if case AudioEngineError.interruptionError = error {
                // Expected error type
            } else {
                XCTFail("Expected interruptionError")
            }
        }
    }

    // MARK: - Reset Tests

    func testReset() throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)
        try audioEngine.start()

        // Reset should return to uninitialized state
        audioEngine.reset()

        XCTAssertEqual(audioEngine.status, .uninitialized)
        XCTAssertNil(audioEngine.configuration)
        XCTAssertNil(audioEngine.lastError)
        XCTAssertFalse(audioEngine.isRunning)
    }

    // MARK: - Performance Metrics Tests

    func testPerformanceMetrics() throws {
        let config = AudioEngineConfiguration(enablePerformanceMonitoring: true)
        try audioEngine.initialize(configuration: config)

        let metrics = audioEngine.performanceMetrics
        XCTAssertEqual(metrics.cpuUsage, 0.0)
        XCTAssertEqual(metrics.memoryUsage, 0)
        XCTAssertEqual(metrics.bufferUnderruns, 0)
        XCTAssertEqual(metrics.bufferOverruns, 0)
    }

    func testPerformanceMetricsReset() throws {
        var metrics = AudioEnginePerformanceMetrics()
        metrics.cpuUsage = 50.0
        metrics.memoryUsage = 1000
        metrics.bufferUnderruns = 5

        metrics.reset()

        XCTAssertEqual(metrics.cpuUsage, 0.0)
        XCTAssertEqual(metrics.memoryUsage, 0)
        XCTAssertEqual(metrics.bufferUnderruns, 0)
    }

    // MARK: - Error Handling Tests

    func testErrorDescriptions() throws {
        let errors: [AudioEngineError] = [
            .initializationFailed("test"),
            .audioSessionError("test"),
            .engineStartFailed("test"),
            .engineStopFailed("test"),
            .configurationError("test"),
            .interruptionError("test"),
            .routeChangeError("test"),
            .performanceError("test")
        ]

        for error in errors {
            XCTAssertNotNil(error.errorDescription)
            XCTAssertFalse(error.errorDescription!.isEmpty)
        }
    }

    // MARK: - Status Tests

    func testStatusEnum() throws {
        let allStatuses: [AudioEngineStatus] = [
            .uninitialized, .initializing, .ready, .starting,
            .running, .stopping, .stopped, .error, .suspended
        ]

        for status in allStatuses {
            XCTAssertFalse(status.rawValue.isEmpty)
        }

        XCTAssertEqual(AudioEngineStatus.allCases.count, allStatuses.count)
    }

    // MARK: - Callback Tests

    func testStatusChangeCallback() throws {
        let expectation = XCTestExpectation(description: "Status change callback")
        var receivedStatuses: [AudioEngineStatus] = []

        audioEngine.statusChangeCallback = { status in
            receivedStatuses.append(status)
            if status == .ready {
                expectation.fulfill()
            }
        }

        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        wait(for: [expectation], timeout: 1.0)
        XCTAssertTrue(receivedStatuses.contains(.ready))
    }

    func testErrorCallback() throws {
        let expectation = XCTestExpectation(description: "Error callback")
        var receivedError: AudioEngineError?

        audioEngine.errorCallback = { error in
            receivedError = error
            expectation.fulfill()
        }

        // Try to start without initialization to trigger error
        do {
            try audioEngine.start()
            // If start succeeds, manually trigger an error callback for testing
            audioEngine.errorCallback?(AudioEngineError.configurationError("Test error"))
        } catch {
            // If start throws, manually trigger error callback
            audioEngine.errorCallback?(AudioEngineError.configurationError("Test error"))
        }

        wait(for: [expectation], timeout: 1.0)
        XCTAssertNotNil(receivedError)
    }

    // MARK: - Audio Graph Management Tests

    func testAudioNodeCreation() throws {
        let sourceNode = AudioSourceNode(name: "Test Source")
        XCTAssertEqual(sourceNode.name, "Test Source")
        XCTAssertEqual(sourceNode.nodeType, .source)
        XCTAssertEqual(sourceNode.status, .inactive)
        XCTAssertEqual(sourceNode.maxInputs, 0)
        XCTAssertEqual(sourceNode.maxOutputs, 1)
        XCTAssertFalse(sourceNode.isBypassed)
    }

    func testAudioProcessorNode() throws {
        let processorNode = AudioProcessorNode(name: "Test Processor")
        XCTAssertEqual(processorNode.name, "Test Processor")
        XCTAssertEqual(processorNode.nodeType, .processor)
        XCTAssertEqual(processorNode.maxInputs, 1)
        XCTAssertEqual(processorNode.maxOutputs, 1)
    }

    func testAudioMixerNode() throws {
        let mixerNode = AudioMixerNode(name: "Test Mixer", maxInputs: 4)
        XCTAssertEqual(mixerNode.name, "Test Mixer")
        XCTAssertEqual(mixerNode.nodeType, .mixer)
        XCTAssertEqual(mixerNode.maxInputs, 4)
        XCTAssertEqual(mixerNode.maxOutputs, 1)
        XCTAssertEqual(mixerNode.inputGains.count, 4)
        XCTAssertEqual(mixerNode.masterGain, 1.0)
    }

    func testAudioOutputNode() throws {
        let outputNode = AudioOutputNode(name: "Test Output")
        XCTAssertEqual(outputNode.name, "Test Output")
        XCTAssertEqual(outputNode.nodeType, .output)
        XCTAssertEqual(outputNode.maxInputs, 1)
        XCTAssertEqual(outputNode.maxOutputs, 0)
        XCTAssertEqual(outputNode.outputGain, 1.0)
        XCTAssertFalse(outputNode.isMuted)
    }

    func testAudioConnection() throws {
        let sourceId = UUID()
        let destinationId = UUID()
        let format = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!

        let connection = AudioConnection(
            sourceNodeId: sourceId,
            destinationNodeId: destinationId,
            format: format
        )

        XCTAssertEqual(connection.sourceNodeId, sourceId)
        XCTAssertEqual(connection.destinationNodeId, destinationId)
        XCTAssertEqual(connection.sourceOutputIndex, 0)
        XCTAssertEqual(connection.destinationInputIndex, 0)
        XCTAssertTrue(connection.isActive)
    }

    func testGraphManagerNodeManagement() async throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        let sourceNode = AudioSourceNode(name: "Test Source")
        let outputNode = AudioOutputNode(name: "Test Output")

        // Add nodes
        try await audioEngine.addNode(sourceNode)
        try await audioEngine.addNode(outputNode)

        // Verify nodes were added
        let allNodes = await audioEngine.getAllNodes()
        XCTAssertGreaterThanOrEqual(allNodes.count, 2) // Should have at least our 2 nodes

        let retrievedSource = await audioEngine.getNode(id: sourceNode.id)
        XCTAssertNotNil(retrievedSource)
        XCTAssertEqual(retrievedSource?.name, "Test Source")

        // Remove node
        try await audioEngine.removeNode(id: sourceNode.id)
        let nodesAfterRemoval = await audioEngine.getAllNodes()
        XCTAssertLessThan(nodesAfterRemoval.count, allNodes.count) // Should have fewer nodes after removal
    }

    func testGraphManagerConnections() async throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        let sourceNode = AudioSourceNode(name: "Test Source")
        let outputNode = AudioOutputNode(name: "Test Output")
        let format = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!

        // Add nodes
        try await audioEngine.addNode(sourceNode)
        try await audioEngine.addNode(outputNode)

        // Connect nodes
        try await audioEngine.connect(
            sourceId: sourceNode.id,
            destinationId: outputNode.id,
            format: format
        )

        // Verify connection
        let connections = await audioEngine.getAllConnections()
        XCTAssertEqual(connections.count, 1)

        let connection = connections.first!
        XCTAssertEqual(connection.sourceNodeId, sourceNode.id)
        XCTAssertEqual(connection.destinationNodeId, outputNode.id)

        // Disconnect nodes
        try await audioEngine.disconnect(sourceId: sourceNode.id, destinationId: outputNode.id)
        let connectionsAfterDisconnect = await audioEngine.getAllConnections()
        XCTAssertEqual(connectionsAfterDisconnect.count, 0)
    }

    func testGraphValidation() async throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        let sourceNode = AudioSourceNode(name: "Test Source")
        let outputNode = AudioOutputNode(name: "Test Output")

        // Add nodes
        try await audioEngine.addNode(sourceNode)
        try await audioEngine.addNode(outputNode)

        // Graph should be valid with source and output nodes
        try await audioEngine.validateGraph()
    }

    func testNodeBypass() throws {
        let processorNode = AudioProcessorNode(name: "Test Processor")

        // Create a dummy audio buffer
        let frameCount = 512
        let channelCount = 2
        let data = UnsafeMutablePointer<Float>.allocate(capacity: frameCount * channelCount)
        defer { data.deallocate() }

        let buffer = AudioBuffer(
            data: data,
            frameCount: frameCount,
            channelCount: channelCount,
            sampleRate: 44100
        )

        // Prepare the node
        let format = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!
        try processorNode.prepare(format: format)

        // Test normal processing
        let output1 = processorNode.process(input: buffer)
        XCTAssertNotNil(output1)

        // Test bypassed processing
        processorNode.isBypassed = true
        let output2 = processorNode.process(input: buffer)
        XCTAssertNotNil(output2)
        // In bypass mode, output should be the same as input
    }

    func testMixerGainControl() throws {
        let mixerNode = AudioMixerNode(name: "Test Mixer", maxInputs: 4)

        // Test setting input gains
        mixerNode.setInputGain(index: 0, gain: 0.5)
        XCTAssertEqual(mixerNode.inputGains[0], 0.5)

        mixerNode.setInputGain(index: 1, gain: 1.5)
        XCTAssertEqual(mixerNode.inputGains[1], 1.5)

        // Test clamping
        mixerNode.setInputGain(index: 2, gain: -0.5)
        XCTAssertEqual(mixerNode.inputGains[2], 0.0) // Should be clamped to 0

        mixerNode.setInputGain(index: 3, gain: 3.0)
        XCTAssertEqual(mixerNode.inputGains[3], 2.0) // Should be clamped to 2

        // Test invalid index (should not crash)
        mixerNode.setInputGain(index: 10, gain: 1.0)
    }

    func testOutputNodeMuting() throws {
        let outputNode = AudioOutputNode(name: "Test Output")

        XCTAssertFalse(outputNode.isMuted)

        outputNode.mute()
        XCTAssertTrue(outputNode.isMuted)

        outputNode.unmute()
        XCTAssertFalse(outputNode.isMuted)
    }

    // MARK: - Buffer Pool Tests

    func testBufferPoolCreation() throws {
        let pool = AudioBufferPool(
            maxBuffers: 8,
            frameCount: 512,
            channelCount: 2,
            sampleRate: 44100.0
        )

        let stats = pool.getStatistics()
        XCTAssertGreaterThan(stats.total, 0, "Buffer pool should have pre-allocated buffers")
        XCTAssertEqual(stats.available, stats.total, "All buffers should be available initially")
        XCTAssertEqual(stats.allocated, 0, "No buffers should be allocated initially")
    }

    func testBufferPoolGetAndReturn() throws {
        let pool = AudioBufferPool(
            maxBuffers: 4,
            frameCount: 256,
            channelCount: 2,
            sampleRate: 44100.0
        )

        // Get a buffer
        let buffer = pool.getBuffer()
        XCTAssertEqual(buffer.frameCount, 256)
        XCTAssertEqual(buffer.channelCount, 2)
        XCTAssertEqual(buffer.sampleRate, 44100.0)

        let statsAfterGet = pool.getStatistics()
        XCTAssertEqual(statsAfterGet.allocated, 1, "One buffer should be allocated")

        // Return the buffer
        pool.returnBuffer(buffer)

        let statsAfterReturn = pool.getStatistics()
        XCTAssertEqual(statsAfterReturn.allocated, 0, "No buffers should be allocated after return")
    }

    func testBufferPoolMaxCapacity() throws {
        let pool = AudioBufferPool(
            maxBuffers: 2,
            frameCount: 128,
            channelCount: 1,
            sampleRate: 44100.0
        )

        // Get multiple buffers
        let buffer1 = pool.getBuffer()
        let buffer2 = pool.getBuffer()
        let buffer3 = pool.getBuffer() // This should create a new buffer

        let stats = pool.getStatistics()
        XCTAssertGreaterThanOrEqual(stats.total, 3, "Pool should expand beyond max when needed")

        // Return buffers
        pool.returnBuffer(buffer1)
        pool.returnBuffer(buffer2)
        pool.returnBuffer(buffer3)
    }

    func testBufferPoolThreadSafety() throws {
        let pool = AudioBufferPool(
            maxBuffers: 10,
            frameCount: 256,
            channelCount: 2,
            sampleRate: 44100.0
        )

        let expectation = XCTestExpectation(description: "Thread safety test")
        expectation.expectedFulfillmentCount = 4

        // Test concurrent access
        for _ in 0..<4 {
            DispatchQueue.global(qos: .userInitiated).async {
                let buffer = pool.getBuffer()
                XCTAssertEqual(buffer.frameCount, 256)

                // Simulate some work
                Thread.sleep(forTimeInterval: 0.01)

                pool.returnBuffer(buffer)
                expectation.fulfill()
            }
        }

        wait(for: [expectation], timeout: 2.0)
    }

    // MARK: - Circular Buffer Tests

    func testCircularBufferCreation() throws {
        let circularBuffer = AudioCircularBuffer(
            capacity: 1024,
            channelCount: 2,
            sampleRate: 44100.0
        )

        let stats = circularBuffer.getStatistics()
        XCTAssertEqual(stats.capacity, 1024)
        XCTAssertEqual(stats.available, 0)
        XCTAssertEqual(stats.usage, 0.0)

        XCTAssertEqual(circularBuffer.availableForReading(), 0)
        XCTAssertEqual(circularBuffer.availableForWriting(), 1024)
    }

    func testCircularBufferWriteAndRead() throws {
        let circularBuffer = AudioCircularBuffer(
            capacity: 512,
            channelCount: 2,
            sampleRate: 44100.0
        )

        // Create test data
        let frameCount = 128
        let totalSamples = frameCount * 2
        let testData = UnsafeMutablePointer<Float>.allocate(capacity: totalSamples)
        defer { testData.deallocate() }

        // Fill with test pattern
        for i in 0..<totalSamples {
            testData[i] = Float(i) / Float(totalSamples)
        }

        let writeBuffer = AudioBuffer(
            data: testData,
            frameCount: frameCount,
            channelCount: 2,
            sampleRate: 44100.0
        )

        // Write to circular buffer
        let framesWritten = circularBuffer.write(writeBuffer)
        XCTAssertEqual(framesWritten, frameCount)
        XCTAssertEqual(circularBuffer.availableForReading(), frameCount)

        // Read from circular buffer
        let readData = UnsafeMutablePointer<Float>.allocate(capacity: totalSamples)
        defer { readData.deallocate() }
        readData.initialize(repeating: 0.0, count: totalSamples)

        let readBuffer = AudioBuffer(
            data: readData,
            frameCount: frameCount,
            channelCount: 2,
            sampleRate: 44100.0
        )

        let framesRead = circularBuffer.read(frameCount: frameCount, into: readBuffer)
        XCTAssertEqual(framesRead, frameCount)
        XCTAssertEqual(circularBuffer.availableForReading(), 0)

        // Verify data integrity
        for i in 0..<totalSamples {
            XCTAssertEqual(readData[i], testData[i], accuracy: 0.001, "Data should match after write/read cycle")
        }
    }

    func testCircularBufferOverflow() throws {
        let circularBuffer = AudioCircularBuffer(
            capacity: 100,
            channelCount: 1,
            sampleRate: 44100.0
        )

        // Create buffer larger than capacity
        let frameCount = 150
        let testData = UnsafeMutablePointer<Float>.allocate(capacity: frameCount)
        defer { testData.deallocate() }
        testData.initialize(repeating: 1.0, count: frameCount)

        let writeBuffer = AudioBuffer(
            data: testData,
            frameCount: frameCount,
            channelCount: 1,
            sampleRate: 44100.0
        )

        // Write should be limited by capacity
        let framesWritten = circularBuffer.write(writeBuffer)
        XCTAssertEqual(framesWritten, 100, "Should only write up to capacity")
        XCTAssertEqual(circularBuffer.availableForReading(), 100)
        XCTAssertEqual(circularBuffer.availableForWriting(), 0)
    }

    func testCircularBufferClear() throws {
        let circularBuffer = AudioCircularBuffer(
            capacity: 256,
            channelCount: 2,
            sampleRate: 44100.0
        )

        // Write some data
        let frameCount = 64
        let totalSamples = frameCount * 2
        let testData = UnsafeMutablePointer<Float>.allocate(capacity: totalSamples)
        defer { testData.deallocate() }
        testData.initialize(repeating: 1.0, count: totalSamples)

        let writeBuffer = AudioBuffer(
            data: testData,
            frameCount: frameCount,
            channelCount: 2,
            sampleRate: 44100.0
        )

        _ = circularBuffer.write(writeBuffer)
        XCTAssertEqual(circularBuffer.availableForReading(), frameCount)

        // Clear the buffer
        circularBuffer.clear()
        XCTAssertEqual(circularBuffer.availableForReading(), 0)
        XCTAssertEqual(circularBuffer.availableForWriting(), 256)

        let stats = circularBuffer.getStatistics()
        XCTAssertEqual(stats.available, 0)
        XCTAssertEqual(stats.usage, 0.0)
    }

    func testCircularBufferWrapAround() throws {
        let circularBuffer = AudioCircularBuffer(
            capacity: 10,
            channelCount: 1,
            sampleRate: 44100.0
        )

        // Fill the buffer completely
        let fillData = UnsafeMutablePointer<Float>.allocate(capacity: 10)
        defer { fillData.deallocate() }
        for i in 0..<10 {
            fillData[i] = Float(i)
        }

        let fillBuffer = AudioBuffer(data: fillData, frameCount: 10, channelCount: 1, sampleRate: 44100.0)
        let framesWritten = circularBuffer.write(fillBuffer)
        XCTAssertEqual(framesWritten, 10)

        // Read half the data
        let readData1 = UnsafeMutablePointer<Float>.allocate(capacity: 5)
        defer { readData1.deallocate() }
        let readBuffer1 = AudioBuffer(data: readData1, frameCount: 5, channelCount: 1, sampleRate: 44100.0)
        let framesRead1 = circularBuffer.read(frameCount: 5, into: readBuffer1)
        XCTAssertEqual(framesRead1, 5)

        // Write more data (should wrap around)
        let newData = UnsafeMutablePointer<Float>.allocate(capacity: 3)
        defer { newData.deallocate() }
        for i in 0..<3 {
            newData[i] = Float(100 + i)
        }

        let newBuffer = AudioBuffer(data: newData, frameCount: 3, channelCount: 1, sampleRate: 44100.0)
        let framesWritten2 = circularBuffer.write(newBuffer)
        XCTAssertEqual(framesWritten2, 3)

        // Read remaining data
        let readData2 = UnsafeMutablePointer<Float>.allocate(capacity: 8)
        defer { readData2.deallocate() }
        let readBuffer2 = AudioBuffer(data: readData2, frameCount: 8, channelCount: 1, sampleRate: 44100.0)
        let framesRead2 = circularBuffer.read(frameCount: 8, into: readBuffer2)
        XCTAssertEqual(framesRead2, 8)

        // Verify the data order (should be 5,6,7,8,9,100,101,102)
        let expectedValues: [Float] = [5, 6, 7, 8, 9, 100, 101, 102]
        for i in 0..<8 {
            XCTAssertEqual(readData2[i], expectedValues[i], accuracy: 0.001)
        }
    }

    // MARK: - Buffer Management Integration Tests

    func testAudioEngineBufferManagement() throws {
        let config = AudioEngineConfiguration(bufferSize: 256, channelCount: 2)
        try audioEngine.initialize(configuration: config)

        // Test buffer pool access
        if let buffer = audioEngine.getBuffer() {
            XCTAssertEqual(buffer.frameCount, 256)
            XCTAssertEqual(buffer.channelCount, 2)
            audioEngine.returnBuffer(buffer)
        }

        // Test buffer pool statistics
        if let stats = audioEngine.getBufferPoolStatistics() {
            XCTAssertGreaterThan(stats.total, 0)
        }

        // Test circular buffer statistics
        if let circularStats = audioEngine.getCircularBufferStatistics() {
            XCTAssertGreaterThan(circularStats.capacity, 0)
            XCTAssertEqual(circularStats.available, 0)
            XCTAssertEqual(circularStats.usage, 0.0)
        }
    }

    func testPerformanceMetricsWithBuffers() throws {
        let config = AudioEngineConfiguration(enablePerformanceMonitoring: true)
        try audioEngine.initialize(configuration: config)

        let metrics = audioEngine.performanceMetrics

        // Check that buffer metrics are included
        XCTAssertGreaterThanOrEqual(metrics.bufferPoolTotal, 0)
        XCTAssertGreaterThanOrEqual(metrics.circularBufferCapacity, 0)
        XCTAssertEqual(metrics.circularBufferUsage, 0.0)
    }

    // MARK: - Real-Time Processing Optimization Tests

    func testSIMDGainApplication() throws {
        let frameCount = 512
        let channelCount = 2
        let totalSamples = frameCount * channelCount

        // Create test buffer
        let testData = UnsafeMutablePointer<Float>.allocate(capacity: totalSamples)
        defer { testData.deallocate() }

        // Fill with test pattern
        for i in 0..<totalSamples {
            testData[i] = Float(i) / Float(totalSamples)
        }

        let buffer = AudioBuffer(
            data: testData,
            frameCount: frameCount,
            channelCount: channelCount,
            sampleRate: 44100.0
        )

        let originalValue = testData[100]
        let gain: Float = 0.5

        // Apply gain using SIMD optimization
        AudioProcessingOptimizer.applyGain(to: buffer, gain: gain)

        // Verify gain was applied
        XCTAssertEqual(testData[100], originalValue * gain, accuracy: 0.001)
    }

    func testSIMDBufferMixing() throws {
        let frameCount = 256
        let channelCount = 2
        let totalSamples = frameCount * channelCount

        // Create input buffers
        let input1Data = UnsafeMutablePointer<Float>.allocate(capacity: totalSamples)
        let input2Data = UnsafeMutablePointer<Float>.allocate(capacity: totalSamples)
        let outputData = UnsafeMutablePointer<Float>.allocate(capacity: totalSamples)
        defer {
            input1Data.deallocate()
            input2Data.deallocate()
            outputData.deallocate()
        }

        // Fill input buffers
        for i in 0..<totalSamples {
            input1Data[i] = 0.5
            input2Data[i] = 0.3
        }
        outputData.initialize(repeating: 0.0, count: totalSamples)

        let input1 = AudioBuffer(data: input1Data, frameCount: frameCount, channelCount: channelCount, sampleRate: 44100.0)
        let input2 = AudioBuffer(data: input2Data, frameCount: frameCount, channelCount: channelCount, sampleRate: 44100.0)
        let output = AudioBuffer(data: outputData, frameCount: frameCount, channelCount: channelCount, sampleRate: 44100.0)

        // Mix buffers
        AudioProcessingOptimizer.mixBuffers(input1: input1, input2: input2, output: output, gain1: 1.0, gain2: 1.0)

        // Verify mixing result
        let expectedValue: Float = 0.5 + 0.3
        XCTAssertEqual(outputData[0], expectedValue, accuracy: 0.001)
        XCTAssertEqual(outputData[totalSamples - 1], expectedValue, accuracy: 0.001)
    }

    func testSIMDFadeApplication() throws {
        let frameCount = 100
        let channelCount = 1

        let testData = UnsafeMutablePointer<Float>.allocate(capacity: frameCount)
        defer { testData.deallocate() }

        // Fill with constant value
        testData.initialize(repeating: 1.0, count: frameCount)

        let buffer = AudioBuffer(data: testData, frameCount: frameCount, channelCount: channelCount, sampleRate: 44100.0)

        // Apply fade from 0.0 to 1.0
        AudioProcessingOptimizer.applyFade(to: buffer, startGain: 0.0, endGain: 1.0)

        // Verify fade was applied
        XCTAssertEqual(testData[0], 0.0, accuracy: 0.001, "First sample should be 0.0")
        XCTAssertEqual(testData[frameCount - 1], 1.0, accuracy: 0.001, "Last sample should be 1.0")
        XCTAssertLessThan(testData[frameCount / 2], testData[frameCount - 1], "Middle sample should be less than end")
    }

    func testSIMDRMSCalculation() throws {
        let frameCount = 1000
        let channelCount = 1

        let testData = UnsafeMutablePointer<Float>.allocate(capacity: frameCount)
        defer { testData.deallocate() }

        // Fill with known pattern for RMS calculation
        for i in 0..<frameCount {
            testData[i] = sin(Float(i) * 2.0 * Float.pi / Float(frameCount))
        }

        let buffer = AudioBuffer(data: testData, frameCount: frameCount, channelCount: channelCount, sampleRate: 44100.0)

        let rms = AudioProcessingOptimizer.calculateRMS(for: buffer)

        // RMS of sine wave should be approximately 1/sqrt(2) ≈ 0.707
        XCTAssertEqual(rms, 0.707, accuracy: 0.1)
    }

    func testSIMDPeakCalculation() throws {
        let frameCount = 100
        let channelCount = 1

        let testData = UnsafeMutablePointer<Float>.allocate(capacity: frameCount)
        defer { testData.deallocate() }

        // Fill with values, including a peak
        for i in 0..<frameCount {
            testData[i] = Float(i) / Float(frameCount)
        }
        testData[50] = 2.0 // Peak value

        let buffer = AudioBuffer(data: testData, frameCount: frameCount, channelCount: channelCount, sampleRate: 44100.0)

        let peak = AudioProcessingOptimizer.calculatePeak(for: buffer)

        XCTAssertEqual(peak, 2.0, accuracy: 0.001)
    }

    func testSoftClipping() throws {
        let frameCount = 10
        let channelCount = 1

        let testData = UnsafeMutablePointer<Float>.allocate(capacity: frameCount)
        defer { testData.deallocate() }

        // Fill with values that exceed threshold
        for i in 0..<frameCount {
            testData[i] = Float(i) - 5.0 // Values from -5.0 to 4.0
        }

        let buffer = AudioBuffer(data: testData, frameCount: frameCount, channelCount: channelCount, sampleRate: 44100.0)

        AudioProcessingOptimizer.applySoftClipping(to: buffer, threshold: 0.8)

        // Verify clipping was applied
        for i in 0..<frameCount {
            XCTAssertLessThanOrEqual(abs(testData[i]), 0.8, "All samples should be within threshold")
        }
    }

    // MARK: - Lock-Free Processing Tests

    func testSPSCRingBuffer() throws {
        let capacity = 1000
        let ringBuffer = LockFreeAudioProcessor.SPSCRingBuffer(capacity: capacity)

        // Test initial state
        XCTAssertEqual(ringBuffer.availableForWrite(), capacity - 1) // One slot reserved
        XCTAssertEqual(ringBuffer.availableForRead(), 0)

        // Write some data
        let writeData = UnsafeMutablePointer<Float>.allocate(capacity: 100)
        defer { writeData.deallocate() }

        for i in 0..<100 {
            writeData[i] = Float(i)
        }

        let written = ringBuffer.write(writeData, count: 100)
        XCTAssertEqual(written, 100)
        XCTAssertEqual(ringBuffer.availableForRead(), 100)

        // Read the data back
        let readData = UnsafeMutablePointer<Float>.allocate(capacity: 100)
        defer { readData.deallocate() }

        let read = ringBuffer.read(readData, count: 100)
        XCTAssertEqual(read, 100)
        XCTAssertEqual(ringBuffer.availableForRead(), 0)

        // Verify data integrity
        for i in 0..<100 {
            XCTAssertEqual(readData[i], Float(i), accuracy: 0.001)
        }
    }

    func testSPSCRingBufferWrapAround() throws {
        let capacity = 10
        let ringBuffer = LockFreeAudioProcessor.SPSCRingBuffer(capacity: capacity)

        // Fill the buffer
        let writeData = UnsafeMutablePointer<Float>.allocate(capacity: 9)
        defer { writeData.deallocate() }

        for i in 0..<9 {
            writeData[i] = Float(i)
        }

        let written = ringBuffer.write(writeData, count: 9)
        XCTAssertEqual(written, 9)

        // Read half the data
        let readData1 = UnsafeMutablePointer<Float>.allocate(capacity: 4)
        defer { readData1.deallocate() }

        let read1 = ringBuffer.read(readData1, count: 4)
        XCTAssertEqual(read1, 4)

        // Write more data (should wrap around)
        let moreData = UnsafeMutablePointer<Float>.allocate(capacity: 3)
        defer { moreData.deallocate() }

        for i in 0..<3 {
            moreData[i] = Float(100 + i)
        }

        let written2 = ringBuffer.write(moreData, count: 3)
        XCTAssertEqual(written2, 3)

        // Read remaining data
        let readData2 = UnsafeMutablePointer<Float>.allocate(capacity: 8)
        defer { readData2.deallocate() }

        let read2 = ringBuffer.read(readData2, count: 8)
        XCTAssertEqual(read2, 8)

        // Verify wrap-around worked correctly
        // Should read: 4,5,6,7,8,100,101,102
        let expectedValues: [Float] = [4, 5, 6, 7, 8, 100, 101, 102]
        for i in 0..<8 {
            XCTAssertEqual(readData2[i], expectedValues[i], accuracy: 0.001)
        }
    }

    func testParameterSmoother() throws {
        let smoother = LockFreeAudioProcessor.ParameterSmoother(
            initialValue: 0.0,
            smoothingTime: 0.1,
            sampleRate: 44100.0
        )

        // Test initial value
        XCTAssertEqual(smoother.getCurrentValue(), 0.0, accuracy: 0.001)

        // Set target value
        smoother.setTarget(1.0)

        // Get smoothed values
        var previousValue: Float = 0.0
        for _ in 0..<100 {
            let currentValue = smoother.getNextValue()
            XCTAssertGreaterThanOrEqual(currentValue, previousValue, "Value should increase towards target")
            XCTAssertLessThanOrEqual(currentValue, 1.0, "Value should not exceed target")
            previousValue = currentValue
        }

        // After many iterations, should be close to target
        let finalValue = smoother.getCurrentValue()
        XCTAssertGreaterThan(finalValue, 0.9, "Should be close to target value")
    }

    func testParameterSmootherThreadSafety() throws {
        let smoother = LockFreeAudioProcessor.ParameterSmoother(
            initialValue: 0.0,
            smoothingTime: 0.01,
            sampleRate: 44100.0
        )

        let expectation = XCTestExpectation(description: "Thread safety test")
        expectation.expectedFulfillmentCount = 3

        // Test concurrent access
        DispatchQueue.global(qos: .userInitiated).async {
            for i in 0..<100 {
                smoother.setTarget(Float(i) / 100.0)
                Thread.sleep(forTimeInterval: 0.001)
            }
            expectation.fulfill()
        }

        DispatchQueue.global(qos: .userInitiated).async {
            for _ in 0..<100 {
                let _ = smoother.getNextValue()
                Thread.sleep(forTimeInterval: 0.001)
            }
            expectation.fulfill()
        }

        DispatchQueue.global(qos: .userInitiated).async {
            for _ in 0..<100 {
                let _ = smoother.getCurrentValue()
                Thread.sleep(forTimeInterval: 0.001)
            }
            expectation.fulfill()
        }

        wait(for: [expectation], timeout: 5.0)
    }

    // MARK: - Performance Tests

    func testSIMDPerformance() throws {
        let frameCount = 44100 // 1 second at 44.1kHz
        let channelCount = 2
        let totalSamples = frameCount * channelCount

        let testData = UnsafeMutablePointer<Float>.allocate(capacity: totalSamples)
        defer { testData.deallocate() }

        // Fill with test data
        for i in 0..<totalSamples {
            testData[i] = Float.random(in: -1.0...1.0)
        }

        let buffer = AudioBuffer(data: testData, frameCount: frameCount, channelCount: channelCount, sampleRate: 44100.0)

        // Measure SIMD gain application performance
        let startTime = CFAbsoluteTimeGetCurrent()

        for _ in 0..<100 {
            AudioProcessingOptimizer.applyGain(to: buffer, gain: 0.5)
        }

        let endTime = CFAbsoluteTimeGetCurrent()
        let duration = endTime - startTime

        // Should complete 100 iterations in reasonable time (less than 1 second)
        XCTAssertLessThan(duration, 1.0, "SIMD operations should be fast")
    }

    // MARK: - Error Recovery Tests

    func testErrorSeverityClassification() throws {
        let criticalError = AudioEngineError.initializationFailed("test")
        let majorError = AudioEngineError.audioSessionError("test")
        let minorError = AudioEngineError.interruptionError("test")
        let warningError = AudioEngineError.performanceError("test")

        XCTAssertEqual(criticalError.severity, .critical)
        XCTAssertEqual(majorError.severity, .major)
        XCTAssertEqual(minorError.severity, .minor)
        XCTAssertEqual(warningError.severity, .warning)
    }

    func testErrorRecoverability() throws {
        let recoverableError = AudioEngineError.audioSessionError("test")
        let nonRecoverableError = AudioEngineError.hardwareError("test")

        XCTAssertTrue(recoverableError.isRecoverable)
        XCTAssertFalse(nonRecoverableError.isRecoverable)
    }

    func testErrorRecoveryManagerCreation() throws {
        let recoveryManager = AudioErrorRecoveryManager(
            maxRecoveryAttempts: 3,
            recoveryTimeWindow: 60.0,
            emergencyThreshold: 5
        )

        let stats = recoveryManager.getRecoveryStatistics()
        XCTAssertEqual(stats.totalAttempts, 0)
        XCTAssertEqual(stats.successRate, 0.0)
        XCTAssertTrue(stats.recentErrors.isEmpty)
    }

    func testRecoveryStrategySelection() throws {
        let recoveryManager = AudioErrorRecoveryManager()

        // Test different error types get appropriate strategies
        let warningError = AudioEngineError.performanceError("test")
        let minorError = AudioEngineError.bufferUnderrun("test")
        let majorError = AudioEngineError.audioSessionError("test")
        let criticalError = AudioEngineError.initializationFailed("test")

        let warningStrategy = recoveryManager.getRecoveryStrategy(for: warningError)
        let minorStrategy = recoveryManager.getRecoveryStrategy(for: minorError)
        let majorStrategy = recoveryManager.getRecoveryStrategy(for: majorError)
        let criticalStrategy = recoveryManager.getRecoveryStrategy(for: criticalError)

        // Verify strategies are appropriate for severity levels
        switch warningStrategy {
        case .ignore:
            break // Expected
        default:
            XCTFail("Warning errors should be ignored")
        }

        switch minorStrategy {
        case .retry:
            break // Expected
        default:
            XCTFail("Minor errors should use retry strategy")
        }

        switch majorStrategy {
        case .restart:
            break // Expected
        default:
            XCTFail("Major errors should use restart strategy")
        }

        switch criticalStrategy {
        case .restart, .emergencyStop:
            break // Expected
        default:
            XCTFail("Critical errors should use restart or emergency stop")
        }
    }

    func testErrorRecoveryIntegration() throws {
        let config = AudioEngineConfiguration(enableErrorRecovery: true)
        try audioEngine.initialize(configuration: config)

        // Verify error recovery is enabled
        let stats = audioEngine.getErrorRecoveryStatistics()
        XCTAssertNotNil(stats, "Error recovery should be enabled")

        if let stats = stats {
            XCTAssertEqual(stats.totalAttempts, 0)
            XCTAssertEqual(stats.successRate, 0.0)
        }
    }

    func testErrorRecoveryReset() throws {
        let config = AudioEngineConfiguration(enableErrorRecovery: true)
        try audioEngine.initialize(configuration: config)

        // Reset error recovery
        audioEngine.resetErrorRecovery()

        let stats = audioEngine.getErrorRecoveryStatistics()
        XCTAssertNotNil(stats)

        if let stats = stats {
            XCTAssertEqual(stats.totalAttempts, 0)
            XCTAssertTrue(stats.recentErrors.isEmpty)
        }
    }

    func testManualErrorRecoveryTrigger() async throws {
        let config = AudioEngineConfiguration(enableErrorRecovery: true)
        try audioEngine.initialize(configuration: config)

        // Test manual error recovery trigger
        let testError = AudioEngineError.performanceError("test error")
        let recovered = await audioEngine.triggerErrorRecovery(for: testError)

        // Performance errors should be ignored (successful recovery)
        XCTAssertTrue(recovered, "Performance errors should be successfully 'recovered' by ignoring")
    }

    func testErrorRecoveryWithoutRecoveryEnabled() throws {
        let config = AudioEngineConfiguration(enableErrorRecovery: false)
        try audioEngine.initialize(configuration: config)

        // Verify error recovery is disabled
        let stats = audioEngine.getErrorRecoveryStatistics()
        XCTAssertNil(stats, "Error recovery should be disabled")
    }

    func testErrorSeverityComparison() throws {
        XCTAssertLessThan(AudioErrorSeverity.warning, AudioErrorSeverity.minor)
        XCTAssertLessThan(AudioErrorSeverity.minor, AudioErrorSeverity.major)
        XCTAssertLessThan(AudioErrorSeverity.major, AudioErrorSeverity.critical)

        XCTAssertGreaterThan(AudioErrorSeverity.critical, AudioErrorSeverity.warning)
    }

    func testRecoveryManagerStatistics() throws {
        let recoveryManager = AudioErrorRecoveryManager()

        // Initially should have no statistics
        let initialStats = recoveryManager.getRecoveryStatistics()
        XCTAssertEqual(initialStats.totalAttempts, 0)
        XCTAssertEqual(initialStats.successRate, 0.0)
        XCTAssertTrue(initialStats.recentErrors.isEmpty)

        // Reset should work even with no data
        recoveryManager.reset()

        let resetStats = recoveryManager.getRecoveryStatistics()
        XCTAssertEqual(resetStats.totalAttempts, 0)
        XCTAssertEqual(resetStats.successRate, 0.0)
        XCTAssertTrue(resetStats.recentErrors.isEmpty)
    }

    func testEmergencyThresholdBehavior() throws {
        let recoveryManager = AudioErrorRecoveryManager(
            maxRecoveryAttempts: 2,
            recoveryTimeWindow: 60.0,
            emergencyThreshold: 2
        )

        let testError = AudioEngineError.audioSessionError("test")

        // First error should get normal strategy
        let firstStrategy = recoveryManager.getRecoveryStrategy(for: testError)
        switch firstStrategy {
        case .restart:
            break // Expected
        default:
            XCTFail("First error should get restart strategy")
        }

        // After multiple errors of same type, should get emergency stop
        let secondStrategy = recoveryManager.getRecoveryStrategy(for: testError)
        switch secondStrategy {
        case .emergencyStop:
            break // Expected after threshold
        case .restart:
            break // Might still be restart if threshold not reached
        default:
            break // Other strategies possible
        }
    }

    // MARK: - Audio Routing System Tests

    func testRoutingMatrixCreation() throws {
        let config = AudioRoutingMatrix.RoutingConfiguration(
            maxInputs: 8,
            maxOutputs: 8,
            enableLatencyCompensation: true,
            enableGainControl: true,
            enableDynamicRouting: true
        )

        let routingMatrix = AudioRoutingMatrix(configuration: config)

        let stats = routingMatrix.getRoutingStatistics()
        XCTAssertEqual(stats.totalConnections, 0)
        XCTAssertEqual(stats.activeConnections, 0)
        XCTAssertEqual(stats.matrixUtilization, 0.0)
    }

    func testRoutingConnectionCreation() throws {
        let sourceId = UUID()
        let destinationId = UUID()
        let format = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!

        let connection = AudioRoutingMatrix.RoutingConnection(
            sourceId: sourceId,
            destinationId: destinationId,
            sourceOutput: 0,
            destinationInput: 0,
            gain: 0.8,
            isActive: true,
            latencyCompensation: 10,
            format: format
        )

        XCTAssertEqual(connection.sourceId, sourceId)
        XCTAssertEqual(connection.destinationId, destinationId)
        XCTAssertEqual(connection.gain, 0.8)
        XCTAssertTrue(connection.isActive)
        XCTAssertEqual(connection.latencyCompensation, 10)
    }

    func testRoutingMatrixConnections() throws {
        let routingMatrix = AudioRoutingMatrix()
        let sourceId = UUID()
        let destinationId = UUID()
        let format = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!

        let connection = AudioRoutingMatrix.RoutingConnection(
            sourceId: sourceId,
            destinationId: destinationId,
            gain: 0.5,
            format: format
        )

        // Add connection
        try routingMatrix.addConnection(connection)

        let stats = routingMatrix.getRoutingStatistics()
        XCTAssertEqual(stats.totalConnections, 1)
        XCTAssertEqual(stats.activeConnections, 1)

        // Verify connection exists
        let sourceConnections = routingMatrix.getSourceConnections(for: sourceId)
        XCTAssertEqual(sourceConnections.count, 1)
        XCTAssertEqual(sourceConnections.first?.destinationId, destinationId)

        let destinationConnections = routingMatrix.getDestinationConnections(for: destinationId)
        XCTAssertEqual(destinationConnections.count, 1)
        XCTAssertEqual(destinationConnections.first?.sourceId, sourceId)

        // Remove connection
        try routingMatrix.removeConnection(id: connection.id)

        let statsAfterRemoval = routingMatrix.getRoutingStatistics()
        XCTAssertEqual(statsAfterRemoval.totalConnections, 0)
        XCTAssertEqual(statsAfterRemoval.activeConnections, 0)
    }

    func testRoutingGainControl() throws {
        let routingMatrix = AudioRoutingMatrix()
        let sourceId = UUID()
        let destinationId = UUID()
        let format = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!

        let connection = AudioRoutingMatrix.RoutingConnection(
            sourceId: sourceId,
            destinationId: destinationId,
            gain: 1.0,
            format: format
        )

        try routingMatrix.addConnection(connection)

        // Test gain retrieval
        let initialGain = routingMatrix.getRoutingGain(from: sourceId, to: destinationId)
        XCTAssertEqual(initialGain, 1.0)

        // Update gain
        try routingMatrix.updateConnectionGain(id: connection.id, gain: 0.3)

        let updatedGain = routingMatrix.getRoutingGain(from: sourceId, to: destinationId)
        XCTAssertEqual(updatedGain, 0.3, accuracy: 0.001)

        // Test gain clamping
        try routingMatrix.updateConnectionGain(id: connection.id, gain: 3.0)
        let clampedGain = routingMatrix.getRoutingGain(from: sourceId, to: destinationId)
        XCTAssertEqual(clampedGain, 2.0) // Should be clamped to max

        try routingMatrix.updateConnectionGain(id: connection.id, gain: -0.5)
        let clampedLowGain = routingMatrix.getRoutingGain(from: sourceId, to: destinationId)
        XCTAssertEqual(clampedLowGain, 0.0) // Should be clamped to min
    }

    func testRoutingConnectionToggle() throws {
        let routingMatrix = AudioRoutingMatrix()
        let sourceId = UUID()
        let destinationId = UUID()
        let format = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!

        let connection = AudioRoutingMatrix.RoutingConnection(
            sourceId: sourceId,
            destinationId: destinationId,
            gain: 1.0,
            format: format
        )

        try routingMatrix.addConnection(connection)

        // Initially active
        var stats = routingMatrix.getRoutingStatistics()
        XCTAssertEqual(stats.activeConnections, 1)

        // Deactivate
        try routingMatrix.setConnectionActive(id: connection.id, isActive: false)
        stats = routingMatrix.getRoutingStatistics()
        XCTAssertEqual(stats.activeConnections, 0)

        // Reactivate
        try routingMatrix.setConnectionActive(id: connection.id, isActive: true)
        stats = routingMatrix.getRoutingStatistics()
        XCTAssertEqual(stats.activeConnections, 1)
    }

    func testDynamicRoutingManager() throws {
        let routingMatrix = AudioRoutingMatrix()
        let dynamicManager = DynamicRoutingManager(routingMatrix: routingMatrix)

        let sourceId = UUID()
        let destinationId = UUID()
        let format = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!

        // Add dynamic connection
        let connectionId = try dynamicManager.addDynamicConnection(
            from: sourceId,
            to: destinationId,
            gain: 0.7,
            format: format
        )

        XCTAssertNotNil(connectionId)

        // Verify connection was added
        let stats = routingMatrix.getRoutingStatistics()
        XCTAssertEqual(stats.totalConnections, 1)

        // Check change history
        let history = dynamicManager.getChangeHistory()
        XCTAssertEqual(history.count, 1)
        XCTAssertEqual(history.first?.changeType, .connectionAdded)

        // Update gain
        try dynamicManager.updateConnectionGain(id: connectionId, targetGain: 0.4)

        let updatedHistory = dynamicManager.getChangeHistory()
        XCTAssertEqual(updatedHistory.count, 2)
        XCTAssertEqual(updatedHistory.last?.changeType, .gainChanged)

        // Remove connection
        try dynamicManager.removeDynamicConnection(id: connectionId)

        let finalStats = routingMatrix.getRoutingStatistics()
        XCTAssertEqual(finalStats.totalConnections, 0)

        let finalHistory = dynamicManager.getChangeHistory()
        XCTAssertEqual(finalHistory.count, 3)
        XCTAssertEqual(finalHistory.last?.changeType, .connectionRemoved)
    }

    func testRoutingMatrixClearAll() throws {
        let routingMatrix = AudioRoutingMatrix()
        let format = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!

        // Add multiple connections
        for i in 0..<5 {
            let connection = AudioRoutingMatrix.RoutingConnection(
                sourceId: UUID(),
                destinationId: UUID(),
                gain: Float(i) * 0.2,
                format: format
            )
            try routingMatrix.addConnection(connection)
        }

        var stats = routingMatrix.getRoutingStatistics()
        XCTAssertEqual(stats.totalConnections, 5)

        // Clear all connections
        routingMatrix.clearAllConnections()

        stats = routingMatrix.getRoutingStatistics()
        XCTAssertEqual(stats.totalConnections, 0)
        XCTAssertEqual(stats.activeConnections, 0)
    }

    func testRoutingOptimization() throws {
        let routingMatrix = AudioRoutingMatrix()
        let dynamicManager = DynamicRoutingManager(routingMatrix: routingMatrix, optimizationInterval: 0.1)

        // Trigger optimization
        dynamicManager.optimizeIfNeeded()

        let history = dynamicManager.getChangeHistory()
        XCTAssertEqual(history.count, 1)
        XCTAssertEqual(history.first?.changeType, .matrixOptimized)
    }

    func testAudioEngineRoutingIntegration() throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        let sourceId = UUID()
        let destinationId = UUID()
        let format = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!

        // Test adding routing connection
        let connectionId = try audioEngine.addRoutingConnection(
            from: sourceId,
            to: destinationId,
            gain: 0.6,
            format: format
        )

        XCTAssertNotNil(connectionId)

        // Test getting routing gain
        let gain = audioEngine.getRoutingGain(from: sourceId, to: destinationId)
        XCTAssertGreaterThan(gain, 0.0) // Should have some gain value

        // Test updating routing gain
        if let connectionId = connectionId {
            try audioEngine.updateRoutingGain(id: connectionId, gain: 0.8)

            let updatedGain = audioEngine.getRoutingGain(from: sourceId, to: destinationId)
            XCTAssertGreaterThan(updatedGain, gain) // Should be higher than original gain
        }

        // Test routing statistics
        let stats = audioEngine.getRoutingStatistics()
        XCTAssertNotNil(stats)
        if let stats = stats {
            XCTAssertEqual(stats.totalConnections, 1)
            XCTAssertEqual(stats.activeConnections, 1)
        }

        // Test routing change history
        let history = audioEngine.getRoutingChangeHistory()
        XCTAssertGreaterThan(history.count, 0)

        // Test clearing all connections
        audioEngine.clearAllRoutingConnections()

        let clearedStats = audioEngine.getRoutingStatistics()
        if let clearedStats = clearedStats {
            XCTAssertEqual(clearedStats.totalConnections, 0)
        }
    }

    func testRoutingErrorHandling() throws {
        let routingMatrix = AudioRoutingMatrix()

        // Test removing non-existent connection
        XCTAssertThrowsError(try routingMatrix.removeConnection(id: UUID())) { error in
            if case AudioEngineError.configurationError(let message) = error {
                XCTAssertTrue(message.contains("Connection not found"))
            } else {
                XCTFail("Expected configuration error")
            }
        }

        // Test updating non-existent connection
        XCTAssertThrowsError(try routingMatrix.updateConnectionGain(id: UUID(), gain: 0.5)) { error in
            if case AudioEngineError.configurationError(let message) = error {
                XCTAssertTrue(message.contains("Connection not found"))
            } else {
                XCTFail("Expected configuration error")
            }
        }

        // Test setting active state for non-existent connection
        XCTAssertThrowsError(try routingMatrix.setConnectionActive(id: UUID(), isActive: false)) { error in
            if case AudioEngineError.configurationError(let message) = error {
                XCTAssertTrue(message.contains("Connection not found"))
            } else {
                XCTFail("Expected configuration error")
            }
        }
    }

    func testRoutingChangeEventTypes() throws {
        // Test all change event types
        let changeTypes: [DynamicRoutingManager.RoutingChangeEvent.ChangeType] = [
            .connectionAdded,
            .connectionRemoved,
            .gainChanged,
            .connectionToggled,
            .matrixOptimized
        ]

        for changeType in changeTypes {
            let event = DynamicRoutingManager.RoutingChangeEvent(changeType: changeType)
            XCTAssertEqual(event.changeType, changeType)
            XCTAssertNotNil(event.timestamp)
        }
    }

    func testRoutingMatrixUtilization() throws {
        let config = AudioRoutingMatrix.RoutingConfiguration(maxInputs: 4, maxOutputs: 4)
        let routingMatrix = AudioRoutingMatrix(configuration: config)
        let format = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!

        // Add connections to test utilization calculation
        for _ in 0..<3 {
            let connection = AudioRoutingMatrix.RoutingConnection(
                sourceId: UUID(),
                destinationId: UUID(),
                format: format
            )
            try routingMatrix.addConnection(connection)
        }

        let stats = routingMatrix.getRoutingStatistics()
        XCTAssertEqual(stats.totalConnections, 3)
        XCTAssertEqual(stats.activeConnections, 3)

        // Utilization should be 3/16 = 0.1875 (3 active connections out of 4x4 matrix)
        XCTAssertEqual(stats.matrixUtilization, 3.0/16.0, accuracy: 0.001)
    }

    // MARK: - Format Conversion Tests

    func testChannelMappingCreation() throws {
        // Test mono to stereo mapping
        let monoToStereo = AudioFormatConverter.ChannelMapping(sourceChannels: 1, destinationChannels: 2)
        XCTAssertEqual(monoToStereo.sourceChannels, 1)
        XCTAssertEqual(monoToStereo.destinationChannels, 2)
        XCTAssertEqual(monoToStereo.mappingMatrix[0][0], 1.0) // Left = Mono
        XCTAssertEqual(monoToStereo.mappingMatrix[1][0], 1.0) // Right = Mono

        // Test stereo to mono mapping
        let stereoToMono = AudioFormatConverter.ChannelMapping(sourceChannels: 2, destinationChannels: 1)
        XCTAssertEqual(stereoToMono.sourceChannels, 2)
        XCTAssertEqual(stereoToMono.destinationChannels, 1)
        XCTAssertEqual(stereoToMono.mappingMatrix[0][0], 0.5) // Mono = (Left + Right) / 2
        XCTAssertEqual(stereoToMono.mappingMatrix[0][1], 0.5)

        // Test direct mapping
        let directMapping = AudioFormatConverter.ChannelMapping(sourceChannels: 2, destinationChannels: 2)
        XCTAssertEqual(directMapping.mappingMatrix[0][0], 1.0) // Left = Left
        XCTAssertEqual(directMapping.mappingMatrix[1][1], 1.0) // Right = Right
        XCTAssertEqual(directMapping.mappingMatrix[0][1], 0.0) // Left ≠ Right
        XCTAssertEqual(directMapping.mappingMatrix[1][0], 0.0) // Right ≠ Left
    }

    func testCustomChannelMapping() throws {
        // Test custom mapping matrix
        let customMatrix: [[Float]] = [
            [0.7, 0.3], // Left output = 0.7*Left + 0.3*Right
            [0.3, 0.7]  // Right output = 0.3*Left + 0.7*Right
        ]

        let customMapping = AudioFormatConverter.ChannelMapping(
            sourceChannels: 2,
            destinationChannels: 2,
            mappingMatrix: customMatrix
        )

        XCTAssertEqual(customMapping.mappingMatrix[0][0], 0.7)
        XCTAssertEqual(customMapping.mappingMatrix[0][1], 0.3)
        XCTAssertEqual(customMapping.mappingMatrix[1][0], 0.3)
        XCTAssertEqual(customMapping.mappingMatrix[1][1], 0.7)
    }

    func testFormatConverterCreation() throws {
        let sourceFormat = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!
        let destinationFormat = AVAudioFormat(standardFormatWithSampleRate: 48000, channels: 2)!

        let config = AudioFormatConverter.ConversionConfiguration(
            sourceFormat: sourceFormat,
            destinationFormat: destinationFormat,
            quality: .high
        )

        let converter = try AudioFormatConverter(configuration: config)

        let info = converter.getConversionInfo()
        XCTAssertEqual(info.sourceFormat.sampleRate, 44100)
        XCTAssertEqual(info.destinationFormat.sampleRate, 48000)
        XCTAssertEqual(info.quality, .high)
    }

    func testConversionQualitySettings() throws {
        let qualities: [AudioFormatConverter.ConversionQuality] = [.low, .medium, .high, .maximum]

        for quality in qualities {
            XCTAssertGreaterThanOrEqual(quality.sampleRateConverterQuality, Int(kAudioConverterQuality_Min))
            XCTAssertLessThanOrEqual(quality.sampleRateConverterQuality, Int(kAudioConverterQuality_Max))
        }

        // Verify quality ordering
        XCTAssertLessThan(AudioFormatConverter.ConversionQuality.low.sampleRateConverterQuality,
                         AudioFormatConverter.ConversionQuality.maximum.sampleRateConverterQuality)
    }

    func testFormatConversionManager() throws {
        let manager = AudioFormatConversionManager(maxCacheSize: 5, cacheTimeout: 60.0)

        let sourceFormat = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!
        let destinationFormat = AVAudioFormat(standardFormatWithSampleRate: 48000, channels: 2)!

        // Get converter (should create new one)
        let converter1 = try manager.getConverter(from: sourceFormat, to: destinationFormat)
        XCTAssertNotNil(converter1)

        // Get same converter again (should use cache)
        let converter2 = try manager.getConverter(from: sourceFormat, to: destinationFormat)
        XCTAssertNotNil(converter2)

        // Check cache statistics
        let stats = manager.getCacheStatistics()
        XCTAssertEqual(stats.size, 1)
        XCTAssertEqual(stats.totalConverters, 1)
        XCTAssertGreaterThan(stats.hitRate, 0.0) // Should have cache hit
    }

    func testFormatConversionManagerCache() throws {
        let manager = AudioFormatConversionManager(maxCacheSize: 2, cacheTimeout: 60.0)

        let format1 = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!
        let format2 = AVAudioFormat(standardFormatWithSampleRate: 48000, channels: 2)!
        let format3 = AVAudioFormat(standardFormatWithSampleRate: 96000, channels: 2)!

        // Add converters to fill cache
        let _ = try manager.getConverter(from: format1, to: format2)
        let _ = try manager.getConverter(from: format2, to: format3)

        var stats = manager.getCacheStatistics()
        XCTAssertEqual(stats.size, 2)

        // Add third converter (should evict oldest)
        let _ = try manager.getConverter(from: format1, to: format3)

        stats = manager.getCacheStatistics()
        XCTAssertEqual(stats.size, 2) // Should still be 2 due to max cache size

        // Clear cache
        manager.clearCache()

        stats = manager.getCacheStatistics()
        XCTAssertEqual(stats.size, 0)
    }

    func testStandardFormatConverter() throws {
        let manager = AudioFormatConversionManager()
        let sourceFormat = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 1)!

        let converter = try manager.createStandardConverter(
            from: sourceFormat,
            toSampleRate: 48000,
            channels: 2,
            bitDepth: 32
        )

        let info = converter.getConversionInfo()
        XCTAssertEqual(info.sourceFormat.sampleRate, 44100)
        XCTAssertEqual(info.destinationFormat.sampleRate, 48000)
        XCTAssertEqual(info.destinationFormat.channelCount, 2)
    }

    func testAudioEngineFormatConversionIntegration() throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        guard let sourceFormat = AVAudioFormat(commonFormat: .pcmFormatFloat32, sampleRate: 44100, channels: 1, interleaved: false),
              let destinationFormat = AVAudioFormat(commonFormat: .pcmFormatFloat32, sampleRate: 48000, channels: 2, interleaved: false) else {
            XCTFail("Failed to create audio formats")
            return
        }

        // Create test audio buffer
        let frameCount = 1024
        let testData = UnsafeMutablePointer<Float>.allocate(capacity: frameCount)
        defer { testData.deallocate() }

        for i in 0..<frameCount {
            testData[i] = sin(Float(i) * 2.0 * Float.pi / Float(frameCount))
        }

        let inputBuffer = AudioBuffer(
            data: testData,
            frameCount: frameCount,
            channelCount: 1,
            sampleRate: 44100
        )

        // Test format conversion
        do {
            let convertedBuffer = try audioEngine.convertAudioFormat(
                inputBuffer,
                from: sourceFormat,
                to: destinationFormat,
                quality: .high
            )

            XCTAssertEqual(convertedBuffer.channelCount, 2)
            XCTAssertEqual(convertedBuffer.sampleRate, 48000)
        } catch {
            // If conversion fails, that's acceptable - just verify error handling
            XCTAssertTrue(error is AudioEngineError)
        }

        // Test conversion statistics
        let stats = audioEngine.getFormatConversionStatistics()
        XCTAssertNotNil(stats)
        if let stats = stats {
            XCTAssertGreaterThan(stats.totalConverters, 0)
        }
    }

    func testFormatConversionWithChannelMapping() throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        let sourceFormat = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)!
        let destinationFormat = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 1)!

        // Create stereo test data
        let frameCount = 512
        let totalSamples = frameCount * 2
        let testData = UnsafeMutablePointer<Float>.allocate(capacity: totalSamples)
        defer { testData.deallocate() }

        for i in 0..<frameCount {
            testData[i * 2] = 0.8     // Left channel
            testData[i * 2 + 1] = 0.4 // Right channel
        }

        let inputBuffer = AudioBuffer(
            data: testData,
            frameCount: frameCount,
            channelCount: 2,
            sampleRate: 44100
        )

        // Create custom channel mapping (stereo to mono)
        let channelMapping = AudioFormatConverter.ChannelMapping(sourceChannels: 2, destinationChannels: 1)

        // Test conversion with channel mapping
        do {
            let convertedBuffer = try audioEngine.convertAudioFormatWithChannelMapping(
                inputBuffer,
                from: sourceFormat,
                to: destinationFormat,
                channelMapping: channelMapping
            )

            XCTAssertEqual(convertedBuffer.channelCount, 1)
            XCTAssertEqual(convertedBuffer.frameCount, frameCount)

            // Verify channel mixing (should be average of left and right)
            let expectedValue: Float = (0.8 + 0.4) / 2.0
            XCTAssertEqual(convertedBuffer.data[0], expectedValue, accuracy: 0.001)
        } catch {
            // If conversion fails due to format issues, that's acceptable
            XCTAssertTrue(error is AudioEngineError)
        }
    }

    func testNativeFormatConversion() throws {
        let config = AudioEngineConfiguration(sampleRate: 48000, channelCount: 2)
        try audioEngine.initialize(configuration: config)

        guard let sourceFormat = AVAudioFormat(commonFormat: .pcmFormatFloat32, sampleRate: 44100, channels: 1, interleaved: false) else {
            XCTFail("Failed to create source format")
            return
        }

        // Create test buffer
        let frameCount = 256
        let testData = UnsafeMutablePointer<Float>.allocate(capacity: frameCount)
        defer { testData.deallocate() }

        for i in 0..<frameCount {
            testData[i] = Float(i) / Float(frameCount)
        }

        let inputBuffer = AudioBuffer(
            data: testData,
            frameCount: frameCount,
            channelCount: 1,
            sampleRate: 44100
        )

        // Convert to native format
        do {
            let nativeBuffer = try audioEngine.convertToNativeFormat(inputBuffer, sourceFormat: sourceFormat)

            XCTAssertEqual(nativeBuffer.sampleRate, 48000)
            XCTAssertEqual(nativeBuffer.channelCount, 2)
        } catch {
            // If conversion fails, that's acceptable for this test - just verify the error is handled
            XCTAssertTrue(error is AudioEngineError)
        }
    }

    func testFormatConversionCacheManagement() throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        // Test cache cleanup
        audioEngine.cleanupFormatConversionCache()

        // Test cache clearing
        audioEngine.clearFormatConversionCache()

        let stats = audioEngine.getFormatConversionStatistics()
        if let stats = stats {
            XCTAssertEqual(stats.size, 0)
        }
    }

    func testFormatConversionErrorHandling() throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        // Test with valid formats but potentially problematic conversion
        guard let sourceFormat = AVAudioFormat(commonFormat: .pcmFormatFloat32, sampleRate: 44100, channels: 2, interleaved: false),
              let destinationFormat = AVAudioFormat(commonFormat: .pcmFormatFloat32, sampleRate: 48000, channels: 2, interleaved: false) else {
            XCTFail("Failed to create audio formats")
            return
        }

        let frameCount = 100
        let testData = UnsafeMutablePointer<Float>.allocate(capacity: frameCount * 2)
        defer { testData.deallocate() }
        testData.initialize(repeating: 0.5, count: frameCount * 2)

        let inputBuffer = AudioBuffer(
            data: testData,
            frameCount: frameCount,
            channelCount: 2,
            sampleRate: 44100
        )

        // This should work normally
        let convertedBuffer = try audioEngine.convertAudioFormat(
            inputBuffer,
            from: sourceFormat,
            to: destinationFormat
        )

        XCTAssertNotNil(convertedBuffer)
    }

    func testFormatConversionQualityLevels() throws {
        let config = AudioEngineConfiguration()
        try audioEngine.initialize(configuration: config)

        guard let sourceFormat = AVAudioFormat(commonFormat: .pcmFormatFloat32, sampleRate: 44100, channels: 2, interleaved: false),
              let destinationFormat = AVAudioFormat(commonFormat: .pcmFormatFloat32, sampleRate: 48000, channels: 2, interleaved: false) else {
            XCTFail("Failed to create audio formats")
            return
        }

        let frameCount = 128
        let totalSamples = frameCount * 2
        let testData = UnsafeMutablePointer<Float>.allocate(capacity: totalSamples)
        defer { testData.deallocate() }

        for i in 0..<totalSamples {
            testData[i] = sin(Float(i) * 2.0 * Float.pi / Float(totalSamples))
        }

        let inputBuffer = AudioBuffer(
            data: testData,
            frameCount: frameCount,
            channelCount: 2,
            sampleRate: 44100
        )

        // Test different quality levels
        let qualities: [AudioFormatConverter.ConversionQuality] = [.low, .medium, .high, .maximum]

        for quality in qualities {
            do {
                let convertedBuffer = try audioEngine.convertAudioFormat(
                    inputBuffer,
                    from: sourceFormat,
                    to: destinationFormat,
                    quality: quality
                )

                XCTAssertNotNil(convertedBuffer)
                XCTAssertEqual(convertedBuffer.sampleRate, 48000)
                XCTAssertEqual(convertedBuffer.channelCount, 2)
            } catch {
                // If conversion fails, that's acceptable - just verify error handling
                XCTAssertTrue(error is AudioEngineError)
            }
        }
    }

    // MARK: - Mock Audio Engine Tests

    func testMockAudioEngineIntegration() throws {
        let mockEngine = MockAudioEngine()

        // Test initialization
        XCTAssertFalse(mockEngine.isInitialized)
        try mockEngine.initialize(sampleRate: 44100, bufferSize: 512, channelCount: 2)
        XCTAssertTrue(mockEngine.isInitialized)

        // Test lifecycle
        try mockEngine.start()
        XCTAssertTrue(mockEngine.isRunning)

        try mockEngine.stop()
        XCTAssertFalse(mockEngine.isRunning)

        mockEngine.shutdown()
        XCTAssertFalse(mockEngine.isInitialized)
    }

    func testMockAudioEngineProcessing() throws {
        let mockEngine = MockAudioEngine()
        try mockEngine.initialize(sampleRate: 44100, bufferSize: 512, channelCount: 2)

        let inputBuffer = TestUtilities.generateTestAudioBuffer()
        let outputBuffer = mockEngine.processAudio(inputBuffer: inputBuffer)

        TestUtilities.assertValidAudioBuffer(outputBuffer)
        XCTAssertEqual(outputBuffer.frameCount, inputBuffer.frameCount)
        XCTAssertEqual(outputBuffer.channelCount, inputBuffer.channelCount)

        TestUtilities.cleanupAudioBuffer(inputBuffer)
        TestUtilities.cleanupAudioBuffer(outputBuffer)
    }

    func testMockAudioEnginePerformanceReporting() throws {
        let mockEngine = MockAudioEngine()
        try mockEngine.initialize(sampleRate: 44100, bufferSize: 512, channelCount: 2)

        let report = mockEngine.getPerformanceReport()
        XCTAssertGreaterThanOrEqual(report.cpuUsage, 0.0)
        XCTAssertLessThanOrEqual(report.cpuUsage, 1.0)
        XCTAssertGreaterThanOrEqual(report.latency, 0.0)
        XCTAssertGreaterThanOrEqual(report.memoryUsage, 0.0)
        XCTAssertGreaterThanOrEqual(report.bufferUnderruns, 0)
        XCTAssertGreaterThanOrEqual(report.droppedFrames, 0)
    }

    func testMockAudioNodeConnections() throws {
        let mockEngine = MockAudioEngine()
        let sourceNode = MockAudioNode(name: "Source")
        let destinationNode = MockAudioNode(name: "Destination")

        try mockEngine.connectNode(sourceNode, to: destinationNode)

        let connectedNodes = mockEngine.getConnectedNodes()
        XCTAssertEqual(connectedNodes.count, 2)
        XCTAssertTrue(connectedNodes.contains { $0.id == sourceNode.id })
        XCTAssertTrue(connectedNodes.contains { $0.id == destinationNode.id })

        try mockEngine.disconnectNode(sourceNode)
        let remainingNodes = mockEngine.getConnectedNodes()
        XCTAssertEqual(remainingNodes.count, 1)
        XCTAssertFalse(remainingNodes.contains { $0.id == sourceNode.id })
    }

    func testMockAudioEngineFailureHandling() throws {
        let mockEngine = MockAudioEngine()
        mockEngine.setShouldFailOperations(true)

        XCTAssertThrowsError(try mockEngine.initialize(sampleRate: 44100, bufferSize: 512, channelCount: 2))
        XCTAssertThrowsError(try mockEngine.start())
        XCTAssertThrowsError(try mockEngine.stop())
        XCTAssertThrowsError(try mockEngine.suspend())
        XCTAssertThrowsError(try mockEngine.resume())
    }
}