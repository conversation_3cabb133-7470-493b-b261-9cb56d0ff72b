import XCTest

/// Tests for code coverage utilities and analysis
final class CoverageUtilitiesTests: DigitonePadTestCase {
    
    var tempDirectory: URL!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        // Create temporary directory for test files
        tempDirectory = FileManager.default.temporaryDirectory
            .appendingPathComponent("CoverageTests_\(UUID().uuidString)")
        try FileManager.default.createDirectory(at: tempDirectory, withIntermediateDirectories: true)
    }
    
    override func tearDownWithError() throws {
        // Clean up temporary directory
        if let tempDirectory = tempDirectory {
            try? FileManager.default.removeItem(at: tempDirectory)
        }
        tempDirectory = nil
        try super.tearDownWithError()
    }
    
    // MARK: - Coverage Analysis Tests
    
    func testAnalyzeCoverage() throws {
        // WHEN: Analyzing coverage
        let report = CoverageUtilities.analyzeCoverage()
        
        // THEN: Report should be generated with valid data
        XCTAssertGreaterThan(report.overallLineCoverage, 0.0)
        XCTAssertLessThanOrEqual(report.overallLineCoverage, 100.0)
        
        XCTAssertGreaterThan(report.overallBranchCoverage, 0.0)
        XCTAssertLessThanOrEqual(report.overallBranchCoverage, 100.0)
        
        XCTAssertGreaterThan(report.overallFunctionCoverage, 0.0)
        XCTAssertLessThanOrEqual(report.overallFunctionCoverage, 100.0)
        
        // Should have coverage data for all expected modules
        let expectedModules = [
            "DataLayer", "AudioEngine", "SequencerModule", "VoiceModule",
            "FilterModule", "FXModule", "MIDIModule", "UIComponents",
            "MachineProtocols", "AppShell"
        ]
        
        for moduleName in expectedModules {
            XCTAssertNotNil(report.moduleCoverage[moduleName], "Missing coverage for \(moduleName)")
        }
    }
    
    func testModuleCoverageData() throws {
        // GIVEN: Coverage report
        let report = CoverageUtilities.analyzeCoverage()
        
        // WHEN: Examining module coverage
        guard let audioEngineModule = report.moduleCoverage["AudioEngine"] else {
            XCTFail("AudioEngine module not found")
            return
        }
        
        // THEN: Module should have valid coverage data
        XCTAssertEqual(audioEngineModule.name, "AudioEngine")
        XCTAssertGreaterThan(audioEngineModule.lineCoverage, 0.0)
        XCTAssertLessThanOrEqual(audioEngineModule.lineCoverage, 100.0)
        
        XCTAssertGreaterThan(audioEngineModule.linesCovered, 0)
        XCTAssertGreaterThan(audioEngineModule.linesTotal, audioEngineModule.linesCovered)
        
        XCTAssertGreaterThan(audioEngineModule.branchesCovered, 0)
        XCTAssertGreaterThan(audioEngineModule.branchesTotal, audioEngineModule.branchesCovered)
        
        XCTAssertGreaterThan(audioEngineModule.functionsCovered, 0)
        XCTAssertGreaterThan(audioEngineModule.functionsTotal, audioEngineModule.functionsCovered)
    }
    
    func testCoverageThresholds() throws {
        // GIVEN: Coverage thresholds
        let thresholds = CoverageUtilities.CoverageThresholds(
            lineCoverageTarget: 90.0,
            branchCoverageTarget: 85.0,
            functionCoverageTarget: 95.0
        )
        
        // THEN: Thresholds should be set correctly
        XCTAssertEqual(thresholds.lineCoverageTarget, 90.0)
        XCTAssertEqual(thresholds.branchCoverageTarget, 85.0)
        XCTAssertEqual(thresholds.functionCoverageTarget, 95.0)
    }
    
    // MARK: - Coverage Assertions Tests
    
    func testAssertCoverageThresholdsSuccess() throws {
        // GIVEN: High coverage report
        let highCoverageReport = createMockCoverageReport(
            lineCoverage: 95.0,
            branchCoverage: 90.0,
            functionCoverage: 98.0
        )
        
        // WHEN & THEN: Assertions should pass
        XCTAssertNoThrow(
            CoverageUtilities.assertCoverageThresholds(highCoverageReport)
        )
    }
    
    func testAssertCoverageThresholdsFailure() throws {
        // GIVEN: Low coverage report
        let lowCoverageReport = createMockCoverageReport(
            lineCoverage: 70.0,
            branchCoverage: 60.0,
            functionCoverage: 80.0
        )
        
        // WHEN & THEN: Assertions should fail (but we can't test XCTAssert failures directly)
        // In a real test environment, these would trigger test failures
        // For now, we just verify the report has low coverage
        XCTAssertLessThan(lowCoverageReport.overallLineCoverage, 90.0)
        XCTAssertLessThan(lowCoverageReport.overallBranchCoverage, 85.0)
        XCTAssertLessThan(lowCoverageReport.overallFunctionCoverage, 95.0)
    }
    
    func testAssertModuleCoverage() throws {
        // GIVEN: Coverage report with specific module data
        let report = CoverageUtilities.analyzeCoverage()
        
        // WHEN & THEN: Module assertions should work
        if let audioEngineModule = report.moduleCoverage["AudioEngine"] {
            // Test with lenient thresholds that should pass
            XCTAssertNoThrow(
                CoverageUtilities.assertModuleCoverage(
                    "AudioEngine",
                    in: report,
                    minimumLineCoverage: 50.0,
                    minimumBranchCoverage: 40.0,
                    minimumFunctionCoverage: 60.0
                )
            )
        }
    }
    
    // MARK: - Report Generation Tests
    
    func testGenerateJSONReport() throws {
        // GIVEN: Coverage report
        let report = CoverageUtilities.analyzeCoverage()
        
        // WHEN: Generating JSON report
        let jsonData = CoverageUtilities.generateJSONReport(report)
        
        // THEN: JSON data should be generated
        XCTAssertNotNil(jsonData)
        
        // Verify JSON can be parsed back
        if let data = jsonData {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            
            XCTAssertNoThrow(try decoder.decode(CoverageUtilities.CoverageReport.self, from: data))
        }
    }
    
    func testGenerateTextReport() throws {
        // GIVEN: Coverage report
        let report = CoverageUtilities.analyzeCoverage()
        
        // WHEN: Generating text report
        let textReport = CoverageUtilities.generateTextReport(report)
        
        // THEN: Text report should contain expected content
        XCTAssertTrue(textReport.contains("DigitonePad Code Coverage Report"))
        XCTAssertTrue(textReport.contains("Overall Coverage:"))
        XCTAssertTrue(textReport.contains("Module Coverage:"))
        XCTAssertTrue(textReport.contains("Line Coverage:"))
        XCTAssertTrue(textReport.contains("Branch Coverage:"))
        XCTAssertTrue(textReport.contains("Function Coverage:"))
        
        // Should contain module names
        XCTAssertTrue(textReport.contains("AudioEngine"))
        XCTAssertTrue(textReport.contains("DataLayer"))
        XCTAssertTrue(textReport.contains("SequencerModule"))
    }
    
    func testSaveCoverageReportJSON() throws {
        // GIVEN: Coverage report
        let report = CoverageUtilities.analyzeCoverage()
        
        // WHEN: Saving JSON report
        try CoverageUtilities.saveCoverageReport(
            report,
            to: tempDirectory,
            format: .json
        )
        
        // THEN: File should be created
        let files = try FileManager.default.contentsOfDirectory(at: tempDirectory, includingPropertiesForKeys: nil)
        let jsonFiles = files.filter { $0.pathExtension == "json" }
        
        XCTAssertEqual(jsonFiles.count, 1)
        
        // Verify file content
        let fileData = try Data(contentsOf: jsonFiles[0])
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        XCTAssertNoThrow(try decoder.decode(CoverageUtilities.CoverageReport.self, from: fileData))
    }
    
    func testSaveCoverageReportText() throws {
        // GIVEN: Coverage report
        let report = CoverageUtilities.analyzeCoverage()
        
        // WHEN: Saving text report
        try CoverageUtilities.saveCoverageReport(
            report,
            to: tempDirectory,
            format: .text
        )
        
        // THEN: File should be created
        let files = try FileManager.default.contentsOfDirectory(at: tempDirectory, includingPropertiesForKeys: nil)
        let textFiles = files.filter { $0.pathExtension == "txt" }
        
        XCTAssertEqual(textFiles.count, 1)
        
        // Verify file content
        let fileContent = try String(contentsOf: textFiles[0])
        XCTAssertTrue(fileContent.contains("DigitonePad Code Coverage Report"))
    }
    
    // MARK: - Performance Tests
    
    func testCoverageAnalysisPerformance() throws {
        measure {
            _ = CoverageUtilities.analyzeCoverage()
        }
    }
    
    func testJSONReportGenerationPerformance() throws {
        let report = CoverageUtilities.analyzeCoverage()
        
        measure {
            _ = CoverageUtilities.generateJSONReport(report)
        }
    }
    
    func testTextReportGenerationPerformance() throws {
        let report = CoverageUtilities.analyzeCoverage()
        
        measure {
            _ = CoverageUtilities.generateTextReport(report)
        }
    }
    
    // MARK: - Integration Tests
    
    func testEndToEndCoverageWorkflow() throws {
        // GIVEN: Complete coverage workflow
        
        // WHEN: Running full analysis and reporting
        let report = CoverageUtilities.analyzeCoverage()
        
        // Generate both report formats
        let jsonData = CoverageUtilities.generateJSONReport(report)
        let textReport = CoverageUtilities.generateTextReport(report)
        
        // Save reports to files
        try CoverageUtilities.saveCoverageReport(report, to: tempDirectory, format: .json)
        try CoverageUtilities.saveCoverageReport(report, to: tempDirectory, format: .text)
        
        // THEN: All steps should complete successfully
        XCTAssertNotNil(jsonData)
        XCTAssertFalse(textReport.isEmpty)
        
        let files = try FileManager.default.contentsOfDirectory(at: tempDirectory, includingPropertiesForKeys: nil)
        XCTAssertEqual(files.count, 2) // JSON and text files
        
        // Verify recommendations are generated
        XCTAssertFalse(report.recommendations.isEmpty)
        
        // Verify timestamp is recent
        let timeDifference = Date().timeIntervalSince(report.timestamp)
        XCTAssertLessThan(timeDifference, 60.0) // Generated within last minute
    }
    
    // MARK: - Helper Methods
    
    private func createMockCoverageReport(
        lineCoverage: Double,
        branchCoverage: Double,
        functionCoverage: Double
    ) -> CoverageUtilities.CoverageReport {
        let mockModule = CoverageUtilities.ModuleCoverage(
            name: "MockModule",
            lineCoverage: lineCoverage,
            branchCoverage: branchCoverage,
            functionCoverage: functionCoverage,
            linesCovered: Int(lineCoverage),
            linesTotal: 100,
            branchesCovered: Int(branchCoverage),
            branchesTotal: 100,
            functionsCovered: Int(functionCoverage),
            functionsTotal: 100
        )
        
        return CoverageUtilities.CoverageReport(
            overallLineCoverage: lineCoverage,
            overallBranchCoverage: branchCoverage,
            overallFunctionCoverage: functionCoverage,
            moduleCoverage: ["MockModule": mockModule],
            thresholds: CoverageUtilities.CoverageThresholds()
        )
    }
}
