{
  "timestamp": "2025-06-15T03:57:24Z",
  "project_root": "/Users/<USER>/padtrack",
  "validation_type": "build_verification",
  "results": {
    "swift_package_build": {
      "status": "failed",
      "message": "Swift Package compilation failed",
      "details": " 74 |         @available(swift, obsoleted: 3, renamed: "allowBluetooth")

/Users/<USER>/padtrack/Sources/AudioEngine/AudioEngine.swift:214:23: error: method cannot be marked @objc because the type of the parameter cannot be represented in Objective-C
212 | 
213 |     /// Initialize the audio engine with the given configuration
214 |     @objc public func initialize(configuration: AudioEngineConfiguration = AudioEngineConfiguration()) throws {
    |                       |                         `- note: Swift structs cannot be represented in Objective-C
    |                       `- error: method cannot be marked @objc because the type of the parameter cannot be represented in Objective-C
215 |         try queue.sync {
216 |             guard _status == .uninitialized else {",
      "timestamp": "2025-06-15T03:57:25Z"
    },
    "swift_package_tests": {
      "status": "failed",
      "message": "Swift Package tests failed",
      "details": "
/Users/<USER>/padtrack/Sources/AudioEngine/AudioEngine.swift:214:23: error: method cannot be marked @objc because the type of the parameter cannot be represented in Objective-C
212 | 
213 |     /// Initialize the audio engine with the given configuration
214 |     @objc public func initialize(configuration: AudioEngineConfiguration = AudioEngineConfiguration()) throws {
    |                       |                         `- note: Swift structs cannot be represented in Objective-C
    |                       `- error: method cannot be marked @objc because the type of the parameter cannot be represented in Objective-C
215 |         try queue.sync {
216 |             guard _status == .uninitialized else {
error: fatalError",
      "timestamp": "2025-06-15T03:57:27Z"
    },
    "ios_build_ipad_pro_11": {
      "status": "failed",
      "message": "iOS build failed for iPad Pro 11-inch",
      "details": "** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 /Users/<USER>/padtrack/Sources/AudioEngine/AudioEngine.swift (in target 'AudioEngine' from project 'DigitonePad')
	SwiftCompile normal arm64 Compiling\ AudioEngine.swift /Users/<USER>/padtrack/Sources/AudioEngine/AudioEngine.swift (in target 'AudioEngine' from project 'DigitonePad')
	SwiftCompile normal arm64 /Users/<USER>/padtrack/Sources/AudioEngine/AudioEngine.swift (in target 'AudioEngine' from project 'DigitonePad')
	SwiftEmitModule normal arm64 Emitting\ module\ for\ AudioEngine (in target 'AudioEngine' from project 'DigitonePad')
	Building project DigitonePad with scheme DigitonePad
(5 failures)",
      "timestamp": "2025-06-15T03:57:29Z"
    },
    "ios_build_ipad_pro_129": {
      "status": "failed",
      "message": "iOS build failed for iPad Pro 12.9-inch",
      "details": "    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -c -primary-file /Users/<USER>/padtrack/Sources/MIDIModule/MIDIInteractor.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIModule.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIModuleProtocols.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIPresenter.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIRouter.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDISwiftUIView.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIViewController.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIViewModel.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIInteractor.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIInteractor.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIInteractor.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIInteractor.dia -target arm64-apple-ios16.0-simulator -Xllvm -aarch64-use-tbi -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=4 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIModule_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -enable-experimental-feature OpaqueTypeErasure -enable-upcoming-feature StrictConcurrency -enable-bare-slash-regex -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/padtrack -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/padtrack -Xcc -D_LIBCPP_HARDENING_MODE\=_LIBCPP_HARDENING_MODE_DEBUG -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/MIDIModule-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/MIDIModule-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/MIDIModule-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad-fa58f9b9dc693cda2657e914cef342f3-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/MIDIModule-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/DerivedSources -Xcc -DDEBUG\=1 -module-name MIDIModule -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.2 -target-sdk-name iphonesimulator18.2 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIInteractor.o -index-unit-output-path /DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIInteractor.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Index.noindex/DataStore -index-system-modules

** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 /Users/<USER>/padtrack/Sources/AudioEngine/AudioEngine.swift (in target 'AudioEngine' from project 'DigitonePad')
	SwiftEmitModule normal arm64 Emitting\ module\ for\ AudioEngine (in target 'AudioEngine' from project 'DigitonePad')
	Building project DigitonePad with scheme DigitonePad
(3 failures)",
      "timestamp": "2025-06-15T03:57:31Z"
    },
    "ios_build_ipad_air": {
      "status": "failed",
      "message": "iOS build failed for iPad Air",
      "details": "    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -c -primary-file /Users/<USER>/padtrack/Sources/MIDIModule/MIDIInteractor.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIModule.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIModuleProtocols.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIPresenter.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIRouter.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDISwiftUIView.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIViewController.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIViewModel.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIInteractor.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIInteractor.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIInteractor.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIInteractor.dia -target arm64-apple-ios16.0-simulator -Xllvm -aarch64-use-tbi -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=4 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIModule_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -enable-experimental-feature OpaqueTypeErasure -enable-upcoming-feature StrictConcurrency -enable-bare-slash-regex -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/padtrack -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/padtrack -Xcc -D_LIBCPP_HARDENING_MODE\=_LIBCPP_HARDENING_MODE_DEBUG -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/MIDIModule-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/MIDIModule-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/MIDIModule-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad-fa58f9b9dc693cda2657e914cef342f3-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/MIDIModule-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/DerivedSources -Xcc -DDEBUG\=1 -module-name MIDIModule -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.2 -target-sdk-name iphonesimulator18.2 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIInteractor.o -index-unit-output-path /DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIInteractor.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Index.noindex/DataStore -index-system-modules

** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 /Users/<USER>/padtrack/Sources/AudioEngine/AudioEngine.swift (in target 'AudioEngine' from project 'DigitonePad')
	SwiftEmitModule normal arm64 Emitting\ module\ for\ AudioEngine (in target 'AudioEngine' from project 'DigitonePad')
	Building project DigitonePad with scheme DigitonePad
(3 failures)",
      "timestamp": "2025-06-15T03:57:33Z"
    },
    "ios_build_ipad_mini": {
      "status": "failed",
      "message": "iOS build failed for iPad mini",
      "details": "    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -emit-module -experimental-skip-non-inlinable-function-bodies-without-types /Users/<USER>/padtrack/Sources/MIDIModule/MIDIInteractor.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIModule.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIModuleProtocols.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIPresenter.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIRouter.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDISwiftUIView.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIViewController.swift /Users/<USER>/padtrack/Sources/MIDIModule/MIDIViewModel.swift -target arm64-apple-ios16.0-simulator -Xllvm -aarch64-use-tbi -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=4 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIModule_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -enable-experimental-feature OpaqueTypeErasure -enable-upcoming-feature StrictConcurrency -enable-bare-slash-regex -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/padtrack -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/padtrack -Xcc -D_LIBCPP_HARDENING_MODE\=_LIBCPP_HARDENING_MODE_DEBUG -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/MIDIModule-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/MIDIModule-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/MIDIModule-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad-fa58f9b9dc693cda2657e914cef342f3-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/MIDIModule-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/DerivedSources -Xcc -DDEBUG\=1 -module-name MIDIModule -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.2 -target-sdk-name iphonesimulator18.2 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -emit-module-doc-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIModule.swiftdoc -emit-module-source-info-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIModule.swiftsourceinfo -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIModule-Swift.h -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIModule-master-emit-module.dia -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIModule-master-emit-module.d -o /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIModule.swiftmodule -emit-abi-descriptor-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MIDIModule.build/Objects-normal/arm64/MIDIModule.abi.json

** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 /Users/<USER>/padtrack/Sources/AudioEngine/AudioEngine.swift (in target 'AudioEngine' from project 'DigitonePad')
	SwiftEmitModule normal arm64 Emitting\ module\ for\ AudioEngine (in target 'AudioEngine' from project 'DigitonePad')
	Building project DigitonePad with scheme DigitonePad
(3 failures)",
      "timestamp": "2025-06-15T03:57:34Z"
    },
    "dependency_resolution": {
      "status": "passed",
      "message": "All package dependencies resolved",
      "details": "No dependency conflicts detected",
      "timestamp": "2025-06-15T03:57:35Z"
    },
    "build_warnings": {
      "status": "warning",
      "message": "1 build warnings detected",
      "details": "Warnings should be reviewed and addressed",
      "timestamp": "2025-06-15T03:57:35Z"
    }
  },
  "summary": {
    "total_tests": 8,
    "passed_tests": 2,
    "failed_tests": 6,
    "warnings": 1
  }
}
