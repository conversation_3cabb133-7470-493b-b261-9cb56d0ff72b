{"timestamp": "2025-06-30T05:31:05Z", "project_root": "/Users/<USER>/padtrack", "validation_type": "memory_profiling", "device_profiles": {"ipad_pro__11_inch___4th_generation_": {"device_name": "iPad Pro (11-inch) (4th generation)", "status": "failed", "error": "Build failed", "timestamp": "2025-06-30T05:31:06Z"}, "ipad_pro__12_9_inch___6th_generation_": {"device_name": "iPad Pro (12.9-inch) (6th generation)", "status": "failed", "error": "Build failed", "timestamp": "2025-06-30T05:31:08Z"}, "ipad_air__5th_generation_": {"device_name": "iPad Air (5th generation)", "status": "failed", "error": "Build failed", "timestamp": "2025-06-30T05:31:09Z"}, "ipad_mini__6th_generation_": {"device_name": "iPad mini (6th generation)", "status": "failed", "error": "Build failed", "timestamp": "2025-06-30T05:31:10Z"}}, "summary": {"devices_tested": 4, "baseline_established": true, "recommendations": ["Monitor memory usage during extended use", "Implement memory pressure handling", "Consider lazy loading for large datasets", "Profile with real device testing"]}}