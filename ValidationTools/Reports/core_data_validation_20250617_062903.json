{
  "timestamp": "2025-06-17T11:29:03Z",
  "project_root": "/Users/<USER>/padtrack",
  "validation_type": "core_data_validation",
  "core_data_tests": {
    "module_compilation": {
      "status": "passed",
      "message": "DataLayer module compiled without errors",
      "details": "Build completed successfully",
      "metrics": {},
      "timestamp": "2025-06-17T11:29:05Z"
    },
    "test_execution": {
      "status": "failed",
      "message": "DataLayer tests failed",
      "details": "
/Users/<USER>/padtrack/Tests/MockObjects/MockDataLayer.swift:40:34: error: type 'DataLayerError' has no member 'initializationError'
 38 |     public func initialize() throws {
 39 |         if shouldFailOperations {
 40 |             throw DataLayerError.initializationError("Mock initialization failure")
    |                                  `- error: type 'DataLayerError' has no member 'initializationError'
 41 |         }
 42 |         

error: fatalError",
      "metrics": {},
      "timestamp": "2025-06-17T11:29:09Z"
    },
    "model_validation": {
      "status": "passed",
      "message": "Core Data model validated successfully",
      "details": "Entities: 6, Relationships: 18",
      "metrics": {"entity_count": 6, "relationship_count": 18},
      "timestamp": "2025-06-17T11:29:09Z"
    },
    "entity_classes": {
      "status": "passed",
      "message": "All entity classes generated correctly",
      "details": "Found all 6 entity classes",
      "metrics": {"found_classes": 6, "total_classes": 6},
      "timestamp": "2025-06-17T11:29:09Z"
    },
    "stack_services": {
      "status": "passed",
      "message": "All Core Data services present",
      "details": "Found all 4 services",
      "metrics": {"found_services": 4, "total_services": 4},
      "timestamp": "2025-06-17T11:29:09Z"
    },
    "migration_system": {
      "status": "passed",
      "message": "Migration system complete",
      "details": "Found all 2 migration files",
      "metrics": {"found_migration": 2, "total_migration": 2},
      "timestamp": "2025-06-17T11:29:09Z"
    },
    "performance_baseline": {
      "status": "passed",
      "message": "Performance baseline established",
      "details": "All operations within acceptable thresholds",
      "metrics": {"init_time_ms": 194.25, "entity_creation_ms": 7.03, "fetch_time_ms": 18.55, "save_time_ms": 37.38},
      "timestamp": "2025-06-17T11:29:09Z"
    }
  },
  "summary": {
    "total_tests": 7,
    "passed_tests": 6,
    "failed_tests": 1,
    "success_rate": 85.00,
    "overall_status": "failed"
  },
  "recommendations": [
    "Core Data stack is properly configured",
    "All entity classes are generated correctly",
    "Migration system is in place for future schema changes",
    "Performance metrics are within acceptable ranges"
  ]
}
