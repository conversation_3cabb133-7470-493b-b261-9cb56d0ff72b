{
  "timestamp": "2025-07-02T03:09:39Z",
  "project_root": "/Users/<USER>/padtrack",
  "validation_type": "core_data_validation",
  "core_data_tests": {
    "module_compilation": {
      "status": "passed",
      "message": "DataLayer module compiled without errors",
      "details": "Build completed successfully",
      "metrics": {},
      "timestamp": "2025-07-02T03:09:40Z"
    },
    "test_execution": {
      "status": "failed",
      "message": "DataLayer tests failed",
      "details": " 25 | 
/Users/<USER>/padtrack/Sources/VoiceModule/OscillatorModulation.swift:189:17: warning: initialization of immutable value 'phase' was never used; consider replacing with assignment to '_' or removing it
187 |         
188 |         for i in 0..<oversampleFactor {
189 |             let phase = Float(i) / Float(oversampleFactor)
    |                 `- warning: initialization of immutable value 'phase' was never used; consider replacing with assignment to '_' or removing it
190 |             let interpolatedCarrier = carrierSample // In practice, would interpolate
191 |             let interpolatedModulator = modulatorSample // In practice, would interpolate

error: fatalError",
      "metrics": {},
      "timestamp": "2025-07-02T03:09:45Z"
    },
    "model_validation": {
      "status": "passed",
      "message": "Core Data model validated successfully",
      "details": "Entities: 6, Relationships: 18",
      "metrics": {"entity_count": 6, "relationship_count": 18},
      "timestamp": "2025-07-02T03:09:45Z"
    },
    "entity_classes": {
      "status": "passed",
      "message": "All entity classes generated correctly",
      "details": "Found all 6 entity classes",
      "metrics": {"found_classes": 6, "total_classes": 6},
      "timestamp": "2025-07-02T03:09:45Z"
    },
    "stack_services": {
      "status": "passed",
      "message": "All Core Data services present",
      "details": "Found all 4 services",
      "metrics": {"found_services": 4, "total_services": 4},
      "timestamp": "2025-07-02T03:09:45Z"
    },
    "migration_system": {
      "status": "passed",
      "message": "Migration system complete",
      "details": "Found all 2 migration files",
      "metrics": {"found_migration": 2, "total_migration": 2},
      "timestamp": "2025-07-02T03:09:45Z"
    },
    "performance_baseline": {
      "status": "passed",
      "message": "Performance baseline established",
      "details": "All operations within acceptable thresholds",
      "metrics": {"init_time_ms": 125.32, "entity_creation_ms": 6.29, "fetch_time_ms": 20.32, "save_time_ms": 20.28},
      "timestamp": "2025-07-02T03:09:45Z"
    }
  },
  "summary": {
    "total_tests": 7,
    "passed_tests": 6,
    "failed_tests": 1,
    "success_rate": 85.00,
    "overall_status": "failed"
  },
  "recommendations": [
    "Core Data stack is properly configured",
    "All entity classes are generated correctly",
    "Migration system is in place for future schema changes",
    "Performance metrics are within acceptable ranges"
  ]
}
