{
  "timestamp": "2025-06-13T21:07:40Z",
  "project_root": "/Users/<USER>/padtrack",
  "validation_type": "protocol_validation",
  "protocol_tests": {
    "module_compilation": {
      "status": "passed",
      "message": "MachineProtocols module compiled without errors",
      "details": "Build completed successfully",
      "timestamp": "2025-06-13T21:07:40Z"
    },
    "protocol_definitions": {
      "status": "failed",
      "message": "Protocol definition validation failed",
      "details": "104 |     func resume() throws { status = .running }
105 |     func reset() { status = .ready }
    |                              `- error: cannot infer contextual base in reference to member 'ready'
106 |     func process(input: AudioBuffer) -> AudioBuffer { return input }
107 |     func updateParameter(key: String, value: Any) throws {}",
      "timestamp": "2025-06-13T21:07:41Z"
    },
    "mock_implementations": {
      "status": "passed",
      "message": "All mock implementations tested successfully",
      "details": "Executed 29 mock implementation tests",
      "timestamp": "2025-06-13T21:07:45Z"
    },
    "parameter_management": {
      "status": "failed",
      "message": "Parameter management validation failed",
      "details": "16 | 
17 |     let param2 = Parameter(
   |                  `- error: cannot find 'Parameter' in scope
18 |         id: "frequency",
19 |         name: "Frequency",",
      "timestamp": "2025-06-13T21:07:45Z"
    },
    "audio_buffer": {
      "status": "failed",
      "message": "Audio buffer validation failed",
      "details": "17 |     // Test empty buffer creation
18 |     let emptyBuffer = AudioBuffer.empty(
   |                       `- error: cannot find 'AudioBuffer' in scope
19 |         sampleRate: 44100.0,
20 |         channelCount: 2,",
      "timestamp": "2025-06-13T21:07:45Z"
    }
  },
  "summary": {
    "total_tests": 5,
    "passed_tests": 2,
    "failed_tests": 3,
    "success_rate": 40.00,
    "overall_status": "failed"
  },
  "recommendations": [
    "All protocol definitions are properly structured",
    "Mock implementations provide good test coverage",
    "Parameter management system is robust",
    "Audio buffer system is ready for production use"
  ]
}
