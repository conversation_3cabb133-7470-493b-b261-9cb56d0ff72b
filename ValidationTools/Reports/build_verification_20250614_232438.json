{
  "timestamp": "2025-06-15T04:24:38Z",
  "project_root": "/Users/<USER>/padtrack",
  "validation_type": "build_verification",
  "results": {
    "swift_package_build": {
      "status": "failed",
      "message": "Swift Package compilation failed",
      "details": " 321 |     public var outputGain: Float = 1.0
 322 |     public var isMuted: Bool = false

/Users/<USER>/padtrack/Sources/AudioEngine/AudioEngine.swift:321:16: error: stored property 'outputGain' of 'Sendable'-conforming class 'AudioOutputNode' is mutable
 319 | /// Audio output node (outputs audio)
 320 | public class AudioOutputNode: BaseAudioNode {
 321 |     public var outputGain: Float = 1.0
     |                `- error: stored property 'outputGain' of 'Sendable'-conforming class 'AudioOutputNode' is mutable
 322 |     public var isMuted: Bool = false
 323 | ",
      "timestamp": "2025-06-15T04:24:40Z"
    },
    "swift_package_tests": {
      "status": "failed",
      "message": "Swift Package tests failed",
      "details": " 322 |     public var isMuted: Bool = false

/Users/<USER>/padtrack/Sources/AudioEngine/AudioEngine.swift:321:16: error: stored property 'outputGain' of 'Sendable'-conforming class 'AudioOutputNode' is mutable
 319 | /// Audio output node (outputs audio)
 320 | public class AudioOutputNode: BaseAudioNode {
 321 |     public var outputGain: Float = 1.0
     |                `- error: stored property 'outputGain' of 'Sendable'-conforming class 'AudioOutputNode' is mutable
 322 |     public var isMuted: Bool = false
 323 | 
error: fatalError",
      "timestamp": "2025-06-15T04:24:41Z"
    },
