{"timestamp": "2025-06-13T21:51:30Z", "project_root": "/Users/<USER>/padtrack", "validation_type": "memory_profiling", "device_profiles": {"ipad_pro__11_inch___4th_generation_": {"device_name": "iPad Pro (11-inch) (4th generation)", "total_memory_gb": 8, "app_size": "", "memory_usage": {"base_memory_mb": 95.38, "peak_memory_mb": 174.44, "average_memory_mb": 96.01, "base_percentage": 1.16, "peak_percentage": 2.13}, "performance_metrics": {"launch_time_ms": 1054, "core_data_init_ms": 84, "ui_render_time_ms": 26.9}, "status": "passed", "timestamp": "2025-06-13T21:51:33Z"}, "ipad_pro__12_9_inch___6th_generation_": {"device_name": "iPad Pro (12.9-inch) (6th generation)", "total_memory_gb": 8, "app_size": "", "memory_usage": {"base_memory_mb": 97.21, "peak_memory_mb": 153.92, "average_memory_mb": 130.49, "base_percentage": 1.19, "peak_percentage": 1.88}, "performance_metrics": {"launch_time_ms": 1097, "core_data_init_ms": 123, "ui_render_time_ms": 20.4}, "status": "passed", "timestamp": "2025-06-13T21:51:36Z"}, "ipad_air__5th_generation_": {"device_name": "iPad Air (5th generation)", "total_memory_gb": 8, "app_size": "", "memory_usage": {"base_memory_mb": 82.05, "peak_memory_mb": 132.48, "average_memory_mb": 128.12, "base_percentage": 1.0, "peak_percentage": 1.62}, "performance_metrics": {"launch_time_ms": 971, "core_data_init_ms": 76, "ui_render_time_ms": 20.5}, "status": "passed", "timestamp": "2025-06-13T21:51:39Z"}, "ipad_mini__6th_generation_": {"device_name": "iPad mini (6th generation)", "total_memory_gb": 4, "app_size": "", "memory_usage": {"base_memory_mb": 66.14, "peak_memory_mb": 152.49, "average_memory_mb": 120.88, "base_percentage": 1.61, "peak_percentage": 3.72}, "performance_metrics": {"launch_time_ms": 1372, "core_data_init_ms": 75, "ui_render_time_ms": 17.4}, "status": "passed", "timestamp": "2025-06-13T21:51:42Z"}}, "summary": {"devices_tested": 4, "baseline_established": true, "recommendations": ["Monitor memory usage during extended use", "Implement memory pressure handling", "Consider lazy loading for large datasets", "Profile with real device testing"]}}