{
  "timestamp": "2025-06-30T05:31:15Z",
  "project_root": "/Users/<USER>/padtrack",
  "validation_type": "build_verification",
  "results": {
    "swift_package_build": {
      "status": "failed",
      "message": "Swift Package compilation failed",
      "details": "4117 |             defaultValue: 0.0,
4118 |             minValue: 0.0,

/Users/<USER>/padtrack/Sources/MachineProtocols/MachineProtocols.swift:4120:20: error: type 'String?' has no member 'percentage'
4118 |             minValue: 0.0,
4119 |             maxValue: 1.0,
4120 |             unit: .percentage
     |                    `- error: type 'String?' has no member 'percentage'
4121 |         )
4122 |         ",
      "timestamp": "2025-06-30T05:31:17Z"
    },
    "swift_package_tests": {
      "status": "failed",
      "message": "Swift Package tests failed",
      "details": "4122 |         
[4/9] Compiling MachineProtocols ParameterManager.swift
/Users/<USER>/padtrack/Sources/MachineProtocols/ParameterManager.swift:5:14: error: invalid redeclaration of 'ObservableParameterManager'
 3 | 
 4 | @MainActor
 5 | public class ObservableParameterManager: ObservableObject {
   |              `- error: invalid redeclaration of 'ObservableParameterManager'
 6 |     // MARK: - Published Properties
 7 |     @Published public private(set) var parameters: [String: Parameter] = [:]
error: fatalError",
      "timestamp": "2025-06-30T05:31:18Z"
    },
    "ios_build_ipad_pro_11": {
      "status": "failed",
      "message": "iOS build failed for iPad Pro 11-inch",
      "details": "    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MachineProtocols.build/empty-MachineProtocols.plist -producttype com.apple.product-type.framework -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator/MachineProtocols.framework/Info.plist
error: failed to deserialize Info.plist task context: fopen(/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/XCBuildData/8167c2a5b47c57c7d1ff96587e5e7a7e.xcbuilddata/attachments/8a59052334a8e0292ef4effbb064e4d9, rb): No such file or directory (2) (in target 'MachineProtocols' from project 'DigitonePad')

** BUILD FAILED **


The following build commands failed:
	ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator/MachineProtocols.framework/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/MachineProtocols.build/empty-MachineProtocols.plist (in target 'MachineProtocols' from project 'DigitonePad')
	Building project DigitonePad with scheme DigitonePad
(2 failures)",
      "timestamp": "2025-06-30T05:31:20Z"
    },
