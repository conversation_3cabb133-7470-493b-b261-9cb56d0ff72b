{
  "timestamp": "2025-06-15T03:03:31Z",
  "project_root": "/Users/<USER>/padtrack",
  "validation_type": "protocol_validation",
  "protocol_tests": {
    "module_compilation": {
      "status": "passed",
      "message": "MachineProtocols module compiled without errors",
      "details": "Build completed successfully",
      "timestamp": "2025-06-15T03:03:32Z"
    },
    "protocol_definitions": {
      "status": "passed",
      "message": "All protocol definitions compile and are exercised by test suite",
      "details": "Core protocols, enums, and structures validated through existing tests",
      "timestamp": "2025-06-15T03:03:32Z"
    },
    "mock_implementations": {
      "status": "failed",
      "message": "Mock implementation tests failed",
      "details": "    |                 |- error: sending 'self' risks causing data races
    |                 `- note: task-isolated 'self' is captured by a main actor-isolated closure. main actor-isolated uses in closure may race against later nonisolated uses
196 |                 continuation.resume()
197 |             }
error: fatalError",
      "timestamp": "2025-06-15T03:03:33Z"
    },
    "parameter_management": {
      "status": "passed",
      "message": "Parameter management system validated through test suite",
      "details": "Parameter creation, manipulation, and manager operations tested",
      "timestamp": "2025-06-15T03:03:33Z"
    },
    "audio_buffer": {
      "status": "passed",
      "message": "Audio buffer system validated through test suite",
      "details": "Buffer creation, manipulation, and property access tested",
      "timestamp": "2025-06-15T03:03:33Z"
    }
  },
  "summary": {
    "total_tests": 5,
    "passed_tests": 4,
    "failed_tests": 1,
    "success_rate": 80.00,
    "overall_status": "failed"
  },
  "recommendations": [
    "All protocol definitions are properly structured",
    "Mock implementations provide good test coverage",
    "Parameter management system is robust",
    "Audio buffer system is ready for production use"
  ]
}
