{
  "timestamp": "2025-06-15T03:03:42Z",
  "project_root": "/Users/<USER>/padtrack",
  "validation_type": "core_data_validation",
  "core_data_tests": {
    "module_compilation": {
      "status": "passed",
      "message": "DataLayer module compiled without errors",
      "details": "Build completed successfully",
      "metrics": {},
      "timestamp": "2025-06-15T03:03:42Z"
    },
    "test_execution": {
      "status": "failed",
      "message": "DataLayer tests failed",
      "details": "
/Users/<USER>/padtrack/Sources/MIDIModule/MIDIInteractor.swift:195:17: error: sending 'self' risks causing data races
193 |         return try await withCheckedThrowingContinuation { continuation in
194 |             DispatchQueue.main.async {
195 |                 self.sendMIDIMessage(message)
    |                 |- error: sending 'self' risks causing data races
    |                 `- note: task-isolated 'self' is captured by a main actor-isolated closure. main actor-isolated uses in closure may race against later nonisolated uses
196 |                 continuation.resume()
197 |             }
error: fatalError",
      "metrics": {},
      "timestamp": "2025-06-15T03:03:43Z"
    },
    "model_validation": {
      "status": "passed",
      "message": "Core Data model validated successfully",
      "details": "Entities: 6, Relationships: 18",
      "metrics": {"entity_count": 6, "relationship_count": 18},
      "timestamp": "2025-06-15T03:03:43Z"
    },
    "entity_classes": {
      "status": "passed",
      "message": "All entity classes generated correctly",
      "details": "Found all 6 entity classes",
      "metrics": {"found_classes": 6, "total_classes": 6},
      "timestamp": "2025-06-15T03:03:43Z"
    },
    "stack_services": {
      "status": "passed",
      "message": "All Core Data services present",
      "details": "Found all 4 services",
      "metrics": {"found_services": 4, "total_services": 4},
      "timestamp": "2025-06-15T03:03:43Z"
    },
    "migration_system": {
      "status": "passed",
      "message": "Migration system complete",
      "details": "Found all 2 migration files",
      "metrics": {"found_migration": 2, "total_migration": 2},
      "timestamp": "2025-06-15T03:03:43Z"
    },
    "performance_baseline": {
      "status": "passed",
      "message": "Performance baseline established",
      "details": "All operations within acceptable thresholds",
      "metrics": {"init_time_ms": 121.43, "entity_creation_ms": 6.50, "fetch_time_ms": 11.78, "save_time_ms": 22.98},
      "timestamp": "2025-06-15T03:03:44Z"
    }
  },
  "summary": {
    "total_tests": 7,
    "passed_tests": 6,
    "failed_tests": 1,
    "success_rate": 85.00,
    "overall_status": "failed"
  },
  "recommendations": [
    "Core Data stack is properly configured",
    "All entity classes are generated correctly",
    "Migration system is in place for future schema changes",
    "Performance metrics are within acceptable ranges"
  ]
}
