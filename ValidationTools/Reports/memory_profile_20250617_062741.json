{"timestamp": "2025-06-17T11:27:41Z", "project_root": "/Users/<USER>/padtrack", "validation_type": "memory_profiling", "device_profiles": {"ipad_pro__11_inch___4th_generation_": {"device_name": "iPad Pro (11-inch) (4th generation)", "total_memory_gb": 8, "app_size": "", "memory_usage": {"base_memory_mb": 56.19, "peak_memory_mb": 192.3, "average_memory_mb": 86.35, "base_percentage": 0.69, "peak_percentage": 2.35}, "performance_metrics": {"launch_time_ms": 1020, "core_data_init_ms": 179, "ui_render_time_ms": 20.3}, "status": "passed", "timestamp": "2025-06-17T11:27:44Z"}, "ipad_pro__12_9_inch___6th_generation_": {"device_name": "iPad Pro (12.9-inch) (6th generation)", "total_memory_gb": 8, "app_size": "", "memory_usage": {"base_memory_mb": 76.08, "peak_memory_mb": 183.07, "average_memory_mb": 120.87, "base_percentage": 0.93, "peak_percentage": 2.23}, "performance_metrics": {"launch_time_ms": 859, "core_data_init_ms": 109, "ui_render_time_ms": 29.8}, "status": "passed", "timestamp": "2025-06-17T11:27:47Z"}, "ipad_air__5th_generation_": {"device_name": "iPad Air (5th generation)", "total_memory_gb": 8, "app_size": "", "memory_usage": {"base_memory_mb": 70.32, "peak_memory_mb": 135.19, "average_memory_mb": 111.33, "base_percentage": 0.86, "peak_percentage": 1.65}, "performance_metrics": {"launch_time_ms": 1015, "core_data_init_ms": 117, "ui_render_time_ms": 27.6}, "status": "passed", "timestamp": "2025-06-17T11:27:50Z"}, "ipad_mini__6th_generation_": {"device_name": "iPad mini (6th generation)", "total_memory_gb": 4, "app_size": "", "memory_usage": {"base_memory_mb": 80.15, "peak_memory_mb": 184.27, "average_memory_mb": 98.72, "base_percentage": 1.96, "peak_percentage": 4.5}, "performance_metrics": {"launch_time_ms": 902, "core_data_init_ms": 147, "ui_render_time_ms": 17.7}, "status": "passed", "timestamp": "2025-06-17T11:27:52Z"}}, "summary": {"devices_tested": 4, "baseline_established": true, "recommendations": ["Monitor memory usage during extended use", "Implement memory pressure handling", "Consider lazy loading for large datasets", "Profile with real device testing"]}}