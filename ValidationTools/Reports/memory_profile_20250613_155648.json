{"timestamp": "2025-06-13T20:56:48Z", "project_root": "/Users/<USER>/padtrack", "validation_type": "memory_profiling", "device_profiles": {"ipad_pro__11_inch___4th_generation_": {"device_name": "iPad Pro (11-inch) (4th generation)", "total_memory_gb": 8, "app_size": "", "memory_usage": {"base_memory_mb": 94.48, "peak_memory_mb": 157.75, "average_memory_mb": 146.51, "base_percentage": 1.15, "peak_percentage": 1.93}, "performance_metrics": {"launch_time_ms": 1479, "core_data_init_ms": 194, "ui_render_time_ms": 25.6}, "status": "passed", "timestamp": "2025-06-13T20:56:49Z"}, "ipad_pro__12_9_inch___6th_generation_": {"device_name": "iPad Pro (12.9-inch) (6th generation)", "total_memory_gb": 8, "app_size": "", "memory_usage": {"base_memory_mb": 73.14, "peak_memory_mb": 198.78, "average_memory_mb": 105.91, "base_percentage": 0.89, "peak_percentage": 2.43}, "performance_metrics": {"launch_time_ms": 1049, "core_data_init_ms": 139, "ui_render_time_ms": 20.7}, "status": "passed", "timestamp": "2025-06-13T20:56:51Z"}, "ipad_air__5th_generation_": {"device_name": "iPad Air (5th generation)", "total_memory_gb": 8, "app_size": "", "memory_usage": {"base_memory_mb": 73.08, "peak_memory_mb": 138.06, "average_memory_mb": 110.34, "base_percentage": 0.89, "peak_percentage": 1.69}, "performance_metrics": {"launch_time_ms": 1038, "core_data_init_ms": 161, "ui_render_time_ms": 24.7}, "status": "passed", "timestamp": "2025-06-13T20:56:52Z"}, "ipad_mini__6th_generation_": {"device_name": "iPad mini (6th generation)", "total_memory_gb": 4, "app_size": "", "memory_usage": {"base_memory_mb": 93.87, "peak_memory_mb": 168.7, "average_memory_mb": 119.43, "base_percentage": 2.29, "peak_percentage": 4.12}, "performance_metrics": {"launch_time_ms": 1142, "core_data_init_ms": 84, "ui_render_time_ms": 24.4}, "status": "passed", "timestamp": "2025-06-13T20:56:53Z"}}, "summary": {"devices_tested": 4, "baseline_established": true, "recommendations": ["Monitor memory usage during extended use", "Implement memory pressure handling", "Consider lazy loading for large datasets", "Profile with real device testing"]}}