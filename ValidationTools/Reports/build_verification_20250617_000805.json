{
  "timestamp": "2025-06-17T05:08:05Z",
  "project_root": "/Users/<USER>/padtrack",
  "validation_type": "build_verification",
  "results": {
    "swift_package_build": {
      "status": "failed",
      "message": "Swift Package compilation failed",
      "details": "114 |             length: 16,
115 |             steps: Array(repeating: false, count: 64)

/Users/<USER>/padtrack/Tests/MockObjects/MockDataLayer.swift:115:37: error: cannot convert value of type 'Bool' to expected argument type 'MockStep'
113 |             projectId: projectId,
114 |             length: 16,
115 |             steps: Array(repeating: false, count: 64)
    |                                     `- error: cannot convert value of type 'Bool' to expected argument type 'MockStep'
116 |         )
117 |         ",
      "timestamp": "2025-06-17T05:08:07Z"
    },
    "swift_package_tests": {
      "status": "failed",
      "message": "Swift Package tests failed",
      "details": "
/Users/<USER>/padtrack/Tests/TestUtilities.swift:184:16: warning: capture of 'operation' with non-sendable type '() -> Bool' in a `@Sendable` closure
182 |         
183 |         let timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { timer in
184 |             if operation() {
    |                |- warning: capture of 'operation' with non-sendable type '() -> Bool' in a `@Sendable` closure
    |                `- note: a function type must be marked '@Sendable' to conform to 'Sendable'
185 |                 timer.invalidate()
186 |                 expectation.fulfill()
error: fatalError",
      "timestamp": "2025-06-17T05:08:09Z"
    },
    "ios_build_ipad_pro_11": {
      "status": "failed",
      "message": "iOS build failed for iPad Pro 11-inch",
      "details": "    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Xlinker -reproducible -target arm64-apple-ios16.0-simulator -dynamiclib -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -O0 -L/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator -L/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator -F/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator -F/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator -filelist /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/UIComponents.build/Objects-normal/arm64/UIComponents.LinkFileList -install_name @rpath/UIComponents.framework/UIComponents -Xlinker -rpath -Xlinker @executable_path/Frameworks -dead_strip -Xlinker -object_path_lto -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/UIComponents.build/Objects-normal/arm64/UIComponents_lto.o -Xlinker -export_dynamic -Xlinker -no_deduplicate -Xlinker -objc_abi_version -Xlinker 2 -fobjc-link-runtime -L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator -L/usr/lib/swift -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/UIComponents.build/Objects-normal/arm64/UIComponents.swiftmodule -framework MachineProtocols -compatibility_version 1 -current_version 1 -Xlinker -dependency_info -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/UIComponents.build/Objects-normal/arm64/UIComponents_dependency_info.dat -o /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator/UIComponents.framework/UIComponents

** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 /Users/<USER>/padtrack/Sources/DigitonePad/MainLayoutView.swift (in target 'DigitonePad' from project 'DigitonePad')
	SwiftCompile normal arm64 Compiling\ MainLayoutView.swift /Users/<USER>/padtrack/Sources/DigitonePad/MainLayoutView.swift (in target 'DigitonePad' from project 'DigitonePad')
	Building project DigitonePad with scheme DigitonePad
(3 failures)",
      "timestamp": "2025-06-17T05:08:12Z"
    },
    "ios_build_ipad_pro_129": {
      "status": "failed",
      "message": "iOS build failed for iPad Pro 12.9-inch",
      "details": "    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks -rename /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/Objects-normal/arm64/DigitonePad.abi.json /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator/DigitonePad.swiftmodule/arm64-apple-ios-simulator.abi.json

** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 Compiling\ MainLayoutView.swift /Users/<USER>/padtrack/Sources/DigitonePad/MainLayoutView.swift (in target 'DigitonePad' from project 'DigitonePad')
	SwiftCompile normal arm64 /Users/<USER>/padtrack/Sources/DigitonePad/MainLayoutView.swift (in target 'DigitonePad' from project 'DigitonePad')
	Building project DigitonePad with scheme DigitonePad
(3 failures)",
      "timestamp": "2025-06-17T05:08:13Z"
    },
    "ios_build_ipad_air": {
      "status": "failed",
      "message": "iOS build failed for iPad Air",
      "details": "    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -c /Users/<USER>/padtrack/Sources/DigitonePad/ContentView.swift /Users/<USER>/padtrack/Sources/DigitonePad/DigitonePadApp.swift /Users/<USER>/padtrack/Sources/DigitonePad/MainLayoutView.swift -primary-file /Users/<USER>/padtrack/Sources/DigitonePad/MainLayoutState.swift /Users/<USER>/padtrack/Sources/DigitonePad/Components/HeaderDisplayView.swift /Users/<USER>/padtrack/Sources/DigitonePad/Components/ParameterEncoderSection.swift /Users/<USER>/padtrack/Sources/DigitonePad/Components/StepSequencerGrid.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/Objects-normal/arm64/MainLayoutState.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/Objects-normal/arm64/MainLayoutState.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/Objects-normal/arm64/MainLayoutState.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/Objects-normal/arm64/MainLayoutState.dia -target arm64-apple-ios16.0-simulator -Xllvm -aarch64-use-tbi -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=4 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/Objects-normal/arm64/DigitonePad_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -enable-experimental-feature OpaqueTypeErasure -enable-upcoming-feature StrictConcurrency -enable-bare-slash-regex -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/padtrack -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/padtrack -Xcc -D_LIBCPP_HARDENING_MODE\=_LIBCPP_HARDENING_MODE_DEBUG -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/DigitonePad-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/DigitonePad-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/DigitonePad-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad-fa58f9b9dc693cda2657e914cef342f3-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/DigitonePad-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/DerivedSources -Xcc -DDEBUG\=1 -module-name DigitonePad -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.2 -target-sdk-name iphonesimulator18.2 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Build/Intermediates.noindex/DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/Objects-normal/arm64/MainLayoutState.o -index-unit-output-path /DigitonePad.build/Debug-iphonesimulator/DigitonePad.build/Objects-normal/arm64/MainLayoutState.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/DigitonePad-cedlhfeovofbepdqwxveduaseyta/Index.noindex/DataStore -index-system-modules

** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 Compiling\ MainLayoutView.swift /Users/<USER>/padtrack/Sources/DigitonePad/MainLayoutView.swift (in target 'DigitonePad' from project 'DigitonePad')
	SwiftCompile normal arm64 /Users/<USER>/padtrack/Sources/DigitonePad/MainLayoutView.swift (in target 'DigitonePad' from project 'DigitonePad')
	Building project DigitonePad with scheme DigitonePad
(3 failures)",
      "timestamp": "2025-06-17T05:08:15Z"
    },
    "ios_build_ipad_mini": {
      "status": "failed",
      "message": "iOS build failed for iPad mini",
      "details": "SwiftDriverJobDiscovery normal arm64 Emitting module for DigitonePad (in target 'DigitonePad' from project 'DigitonePad')

** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 Compiling\ MainLayoutView.swift /Users/<USER>/padtrack/Sources/DigitonePad/MainLayoutView.swift (in target 'DigitonePad' from project 'DigitonePad')
	SwiftCompile normal arm64 /Users/<USER>/padtrack/Sources/DigitonePad/MainLayoutView.swift (in target 'DigitonePad' from project 'DigitonePad')
	Building project DigitonePad with scheme DigitonePad
(3 failures)",
      "timestamp": "2025-06-17T05:08:17Z"
    },
    "dependency_resolution": {
      "status": "passed",
      "message": "All package dependencies resolved",
      "details": "No dependency conflicts detected",
      "timestamp": "2025-06-17T05:08:17Z"
    },
    "build_warnings": {
      "status": "failed",
      "message": "94 build warnings detected",
      "details": "Excessive warnings indicate potential issues",
      "timestamp": "2025-06-17T05:08:17Z"
    }
  },
  "summary": {
    "total_tests": 8,
    "passed_tests": 1,
    "failed_tests": 7,
    "warnings": 0
  }
}
