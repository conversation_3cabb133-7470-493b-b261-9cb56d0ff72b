# User Manual


### FCC compliance statement

This device complies with part 15 of the FCC rules. Operation is subject to the following two conditions:
(1) This device may not cause harmful interference, and (2) this device must accept any interference
received, including interference that may cause undesired operation.

NOTE: This equipment has been tested and found to comply with the limits for a Class B digital device,
pursuant to Part 15 of the FCC Rules. These limits are designed to provide reasonable protection
against harmful interference in a residential installation. This equipment generates, uses and can
radiate radio frequency energy and, if not installed and used in accordance with the instructions, may
cause harmful interference to radio communications. However, there is no guarantee that interference
will not occur in a particular installation. If this equipment does cause harmful interference to radio or
television reception, which can be determined by turning the equipment off and on, the user is encour-
aged to try to correct the interference by one or more of the following measures:

- Reorient or relocate the receiving antenna.
- Increase the separation between the equipment and receiver.
- Connect the equipment into an outlet on a circuit different from that to which the receiver is
    connected.
- Consult the dealer or an experienced radio/TV technician for help.

### European Union regulation compliance statement

This product has been tested to comply with the Low Voltage Directive 2014/35/EU and the Electro-
magnetic Compatibility Directive 2014/30/EU. The product meets the requirements of RoHS 2 Directive
2011/65/EU.

```
This symbol indicates that your product must be disposed of properly according to local
laws and regulations.
```
### Legal disclaimer

The information in this document is subject to change without notice and should not be construed as a
commitment by Elektron. Elektron assumes no responsibility for any errors that may appear in this doc-
ument. Elektron may also make improvements and/or changes in the products and programs described
in this document at any time without notice. In no event shall Elektron be liable for any special, indirect,
or consequential damages or any damages whatsoever resulting from loss of use, data, or profits,
whether in an action of contract, negligence, or other action, arising out of or in connection with the use
or performance of this information.

### Canada

This Class B digital apparatus complies with Canadian ICES-003.
Cet appareil numérique de la classe B est conforme à la norme NMB-003.

```
WARNING: Cancer and Reproductive Harm – http://www.P65Warnings.ca.gov
AVERTISSEMENT: Cancer et effet nocif sur la reproduction – http://www.P65Warnings.ca.gov
ADVERTENCIA: Cáncer y Daño Reproductivo – http://www.P65Warnings.ca.gov
```

IMPORTANT SAFETY AND MAINTENANCE INSTRUCTIONS

Please read these instructions carefully and adhere to the operating advice.

1. Do not use this unit near water.
2. Never use aggressive cleaners on the casing or on the screen. Remove dust, dirt and fingerprints with
    a soft, dry and non-abrasive cloth. More persistent dirt can be removed with a slightly damp cloth using
    only water. Disconnect all cables while doing this. Only reconnect them when the product is safely dry.
3. Install in accordance with the manufacturer’s instructions. Make sure you place the unit on a stable sur-
    face before use. If you mount the unit in a rack, be sure to tighten all four screws in the rack mount holes.
4. Connect the unit to an easily accessible electrical outlet close to the unit.
5. When transporting the unit, use accessories recommended by the manufacturer or the original box and
    padding.
6. Do not install near any heat sources such as radiators, heat registers, stoves, or any other equipment
    (including amplifiers) producing heat.
7. Do not put the PL-2S Protective Cover (Elektron accessory) on the unit while the unit is powered on.
8. This product, by itself or in combination with amplifiers, headphones or speakers, is capable of produc-
    ing sound levels that may cause permanent hearing loss. Do not operate at a high volume level or at a
    level that is uncomfortable.
9. Protect the power cord from being walked on or pinched particularly at plugs, convenience receptacles,
    and the point where they exit from the unit.
10. Only use attachments/accessories specified by the manufacturer.
11. Unplug this unit during lightning storms or when it is not used for long periods of time.
12. Refer all servicing to qualified service technicians. Servicing is required when the unit has been damaged
    in any way, liquid has been spilled or objects have fallen into the unit, the unit has been exposed to rain
    or moisture, does not operate normally, or has been dropped.

### WARNING

To reduce the risk of fire, electrical shock or product damage:

- Do not expose the unit to rain, moisture, dripping or splashing and also avoid placing objects filled with
    liquid, such as vases, on the unit.
- Do not expose the unit to direct sunlight, nor use it in ambient temperatures exceeding 40°C as this can
    lead to malfunction.
- Do not open the casing. There are no user repairable or adjustable parts inside. Leave service and
    repairs to trained service technicians only.
- Do not exceed the limitations specified in the Electrical specifications.

### SAFETY INSTRUCTIONS FOR THE POWER ADAPTER

- The adapter is not safety grounded and may only be used indoors.
- To ensure good ventilation for the adapter, do not place it in tight spaces. To prevent risk of electric
    shock and fire because of overheating, ensure that curtains and other objects do not prevent adapter
    ventilation.
- Do not expose the power adapter to direct sunlight, nor use it in ambient temperatures exceeding 40°C.
- Connect the adapter to an easily accessible electrical outlet close to the unit.
- The adapter is in standby mode when the power cord is connected. The primary circuit is always active
    when the cord is connected to the power outlet. Pull out the cord to completely disconnect the adapter.
- In the EU, only use CE approved power cords.

### RESTART

- For a complete restart of the device, wait for at least 30 seconds after turning it off before turning it
    on again.


## TABLE OF CONTENTS





- 1. INTRODUCTION TABLE OF CONTENTS
   - 1.1 CONVENTIONS IN THIS MANUAL
- 2. THE DIGITONE II
- 3. PANEL LAYOUT AND CONNECTORS
   - 3.1 FRONT PANEL
   - 3.2 REAR CONNECTORS.
   - 3.3 SETTING UP AND STARTING THE DIGITONE II
- 4. DIGITONE II SOUND ARCHITECTURE
   - 4.1 AUDIO VOICES
   - 4.2 EFFECTS
      - 4.2.1 SEND EFFECTS
      - 4.2.2 MASTER EFFECTS
- 5. OVERVIEW OF THE DIGITONE II DATA STRUCTURE
   - 5.1 +DRIVE
   - 5.2 DATA STRUCTURE
      - 5.2.1 PROJECT
      - 5.2.2 PATTERNS
      - 5.2.3 PRESETS
   - 5.3 ABOUT THE TRACKS
      - 5.3.1 AUDIO TRACKS AND MACHINES
      - 5.3.2 MIDI TRACKS
      - 5.3.3 EDITING THE TRACKS
- 6. INTERACTING WITH THE DIGITONE II
   - 6.1 SCREEN NAVIGATION
   - 6.2 PARAMETER EDITING
      - 6.2.1 PARAMETER VALUE JUMP
      - 6.2.2 CONTROL ALL
      - 6.2.3 [FUNC] KEY PRESS COMBINATIONS
   - 6.3 QUICK SCROLLING
   - 6.4 COPY, CLEAR, AND PASTE
   - 6.5 THE NAMING SCREEN
      - 6.5.1 POP-UP NAMING
   - 6.6 OVERBRIDGE
   - 6.7 CLASS COMPLIANT DEVICE
   - 6.8 BACKING UP THE DIGITONE II
      - 6.8.1 BACKING UP PROJECTS, PRESETS
      - 6.8.2 TRANSFERRING BACKED UP FILES TO YOUR ELEKTRON DEVICE
- 7. QUICK START
   - 7.1 SELECTING AND PLAYING THE PRESETS
   - 7.2 PLAYING THE FACTORY PRESET PATTERNS.
   - 7.3 USING KEYBOARD MODE
   - 7.4 USING MUTE MODE
   - 7.5 TEMPO AND METRONOME
      - 7.5.1 TEMPO
      - 7.5.2 METRONOME
   - 7.6 EDITING PARAMETERS
- 8. DIGITONE II CONTROLS TABLE OF CONTENTS
   - 8.1 TRIG KEYS
   - 8.2 ROTARY ENCODERS
   - 8.3 KEY BEHAVIOR
   - 8.4 MIDI NOTES
   - 8.5 MODES
      - 8.5.1 KEYBOARD MODE
      - 8.5.2 KEYBOARD SETUP MENU
      - 8.5.3 MUTE MODE
      - 8.5.4 TRIG MODE
- 9. PATTERNS, KITS AND PRESETS
   - 9.1 THE +DRIVE LIBRARY AND THE POOL
      - 9.1.1 ADDING PRESETS TO THE POOL
   - 9.2 PRESET/KIT MENU
      - 9.2.1 LOAD (PRESET)
      - 9.2.2 SAVE (PRESET)
      - 9.2.3 MANAGE (PRESET)
      - 9.2.4 POOL
      - 9.2.5 LOAD (KIT)
      - 9.2.6 SAVE (KIT)
      - 9.2.7 MANAGE (KIT)
   - 9.3 PLAYING A PRESET
      - 9.3.1 PLAYING A PRESET WITH AN EXTERNAL MIDI UNIT
   - 9.4 EDITING A PRESET
   - 9.5 SAVING A PRESET
   - 9.6 THE SETUP MENU
      - 9.6.1 KIT
      - 9.6.2 TRACK
   - 9.7 ARPEGGIATOR MENU
      - 9.7.1 MODE
      - 9.7.2 SPEED
      - 9.7.3 RANGE
      - 9.7.4 N.LEN
      - 9.7.5 LEN
      - 9.7.6 OFS
- 10. THE SEQUENCER
   - 10.1 PATTERN OPERATIONS
      - 10.1.1 SELECTING BANK AND PATTERN
      - 10.1.2 PATTERN CONTROL
      - 10.1.3 CHAINS
   - 10.2 EDITING A PATTERN
      - 10.2.1 TRIG TYPES
   - 10.3 GRID RECORDING MODE
      - 10.3.1 NOTE EDIT MENU
   - 10.4 LIVE RECORDING MODE
   - 10.5 STEP RECORDING MODE
   - 10.6 SEQUENCER MENU (EUCLIDEAN MODE)
   - 10.7 VOICE SETUP MENU
   - 10.8 MICRO TIMING
   - 10.9 RETRIGS
   - 10.10 QUANTIZE MENU
   - 10.11 PAGE SETUP MENU. TABLE OF CONTENTS
      - 10.11.1 PER PATTERN MODE
      - 10.11.2 PER TRACK MODE
   - 10.12 SEQUENCER FEATURES
      - 10.12.1 PARAMETER LOCKS.
      - 10.12.2 PRESET LOCKS
      - 10.12.3 TRIG CONDITIONS AND CONDITIONAL LOCKS
      - 10.12.4 FILL MODE
      - 10.12.5 COPY, PASTE AND CLEAR OPERATIONS
      - 10.12.6 TEMPORARY SAVE AND RELOAD PATTERN COMMANDS
      - 10.12.7 TRACK TRANSPOSE
      - 10.12.8 PATTERN TRANSPOSE
      - 10.12.9 SELECTED TRACKS TRANSPOSE
   - 10.13 SONG MODE
      - 10.13.1 THE SONG EDIT SCREEN
      - 10.13.2 CREATING AND EDITING A SONG
      - 10.13.3 PLAYING A SONG
   - 10.14 PERFORM KIT MODE
      - 10.14.1 PERFORM KIT MODE ACTIONS
- 11. TRACK PARAMETERS
   - 11.1 EDITING THE TRACK PARAMETERS
   - 11.2 TRIG PAGE
   - 11.3 TRIG PAGE
   - 11.4 SYN PAGES
   - 11.5 FLTR PAGE
   - 11.6 FLTR PAGE
   - 11.7 AMP PAGE
   - 11.8 FX PAGE
   - 11.9 MOD PAGE
   - 11.10 MOD PAGE
   - 11.11 MOD PAGE
- 12. FX AND MIXER PARAMETERS
   - 12.1 EDITING THE SEND FX AND MIXER PARAMETERS
   - 12.2 DELAY
   - 12.3 REVERB
   - 12.4 CHORUS
   - 12.5 COMPRESSOR
   - 12.6 INTERNAL MIXER PAGE 1.
   - 12.7 INTERNAL MIXER PAGE
   - 12.8 FX MIXER
   - 12.9 EXTERNAL MIXER
- 13. SETTINGS MENU
   - 13.1 PROJECT
      - 13.1.1 LOAD PROJECT
      - 13.1.2 SAVE PROJECT AS
      - 13.1.3 MANAGE PROJECTS
   - 13.2 SONG
      - 13.2.1 RENAME
      - 13.2.2 CLEAR
      - 13.2.3 LOAD
      - 13.2.4 SAVE TO PROJ TABLE OF CONTENTS
   - 13.3 PATTERN MENU
      - 13.3.1 RENAME
      - 13.3.2 CLEAR
      - 13.3.3 SAVE TO PROJ
      - 13.3.4 RELOAD FROM PROJ
   - 13.4 MIDI CONFIG
      - 13.4.1 SYNC
      - 13.4.2 PORT CONFIG
      - 13.4.3 CHANNELS
   - 13.5 SYSEX DUMP
      - 13.5.1 SYSEX SEND
      - 13.5.2 SYSEX RECEIVE
   - 13.6 AUDIO ROUTING
      - 13.6.1 TO MAIN
      - 13.6.2 TO SEND FX
      - 13.6.3 USB IN
      - 13.6.4 USB OUT
      - 13.6.5 INT TO MAIN
      - 13.6.6 USB TO MAIN [dB]
      - 13.6.7 PRE/POST FADER
   - 13.7 PERSONALIZE
      - 13.7.1 LED INTENSITY
      - 13.7.2 LED BACKLIGHT
      - 13.7.3 REMEMBER SUBPAGE
      - 13.7.4 U/D KEY MODE
      - 13.7.5 PAGE AUTOCOPY
      - 13.7.6 NOTE PARAM
   - 13.8 SYSTEM
      - 13.8.1 USB CONFIG
      - 13.8.2 OS UPGRADE
      - 13.8.3 FORMAT +DRIVE
      - 13.8.4 MASTER TUNE
- 14. STARTUP MENU
   - 14.1 TEST MODE
   - 14.2 EMPTY RESET
   - 14.3 FACTORY RESET
   - 14.4 OS UPGRADE
   - 14.5 EXIT
- 15. SETUP EXAMPLES
   - 15.1 DIGITONE II WITH A MONOPHONIC BASS MACHINE
   - 15.3 CONTROLLING A SYNTHESIZER USING THE MIDI TRACKS
- 16. KEY COMBINATIONS
- 17 TECHNICAL INFORMATION
- 18 CREDITS AND CONTACT INFORMATION
- APPENDIX A: MACHINES
   - A.1 ASSIGNING MACHINES TO THE ACTIVE TRACK
   - A.2 SYN MACHINES
      - A.2.1 FM TONE TABLE OF CONTENTS
      - A.2.2 FM DRUM
      - A.2.3 WAVETONE
      - A.2.4 SWARMER
      - A.2.5 MIDI
   - A.3 FLTR MACHINES.
      - A.3.1 MULTI-MODE
      - A.3.2 LOWPASS
      - A.3.3 LEGACY LP/HP
      - A.3.4 COMB-
      - A.3.5 COMB+
      - A.3.6 EQUALIZER
- APPENDIX B: THE FM TONE SYNTHESIS
   - B.1 OVERVIEW
   - B.2 OPERATORS
   - B.3 ALGORITHMS
   - B.4 FM RATIOS
   - B.5 OPERATOR ENVELOPES
   - B.6 HARMONICS
   - B.7 SYN PAGE 1 PARAMETERS OVERVIEW
- APPENDIX C: MIDI
   - C.1 TRACK PARAMETERS
   - C.2 TRIG PARAMETERS
   - C.3 SOURCE PARAMETERS
   - C.4 FILTER PARAMETERS
   - C.5 AMP PARAMETERS
   - C.6 EUCLIDEAN SEQUENCER PARAMETERS
   - C.7 FX PARAMETERS
   - C.8 MOD PARAMETERS.
   - C.9 SEND FX PARAMETERS
   - C.10 MIXER PARAMETERS
   - C.11 VAL PARAMETERS
   - C.12 MISC PARAMETERS
- APPENDIX D: LFO MODULATION DESTINATIONS
- APPENDIX E: KEYBOARD SCALES
- INDEX


## 1. INTRODUCTION TABLE OF CONTENTS


##### 1. INTRODUCTION

1. INTRODUCTION

```
To get the most out of your Digitone II, we recommend that you read this manual in its entirety.
```
### 1.1 CONVENTIONS IN THIS MANUAL

```
We have used the following conventions throughout the manual:
```
- Key names are written in upper case, bold style and within brackets. For instance, the key labeled
    “FUNC” on the main panel is written as [FUNC].
- Knobs are written in upper case, bold, italic letters. For instance, the knob “Level/Data” is called LEVEL/
    DATA.
- Menu names are written in upper case letters. The SETTINGS menu is an example of that.
- Parameter names and certain menu options where settings can be made or actions performed are writ-
    ten in bold, upper case letters. For example, VOL.
- Upper case letters are used for parameter setting alternatives. For example, OFF.
- Messages visible on the screen are written in upper case letters with quotation marks.
    For example, “QUANTIZE LIVE REC”.
- LED indicators like the Page LEDs are written like this: <PAGE>.

```
The following symbols are used throughout the manual:
```
```
Important information that you should pay attention to.
```
```
A tip that will make it easier for you to interact with the Digitone II.
```

## 2. THE DIGITONE II

2. THE DIGITONE II

We’re delighted to present to you Digitone II. It’s the latest creature in Elektron’s history of digital products
with an FM synthesis heart. But for this one, FM is only half the story.

Let’s rewind a touch. Of course, it is a successor to the wonderful original Digitone, itself a descent into
deciphering and modernizing FM synthesis. Digitone left us pondering. Could we go bigger while maintain-
ing the streamlined approach that fits these boxes so well? Could we go deeper while ensuring the joy of
sound design only got stronger? These were reflections that opened up all manner of exciting avenues,
and was an easier idea to posit than it was to actualize. But it unlatched a door that we couldn’t and frankly
didn’t want to shut. To be honest, the prospect excited the heck out of us.

With Digitone II, we dove into the FM rabbit hole and dug up new ways to access that contrary combination
of metallic, organic, punchy bliss. But this Device doesn’t stop there. We tuned into multiple additional forms
of digital synthesis and brought them into proceedings, meaning there is a huge variety of melodic and per-
cussive options at your disposal. It is a multi-machine, many-realm-reaching behemoth.

Our goal was to have something ripe for exploration - capable of impressive sound sculpting but that just
demands to be played and enjoyed. And if that sounds up your alley we think you’re in for a proper adven-
ture. Every single time you dive in. If you haven’t done so already, flick the power switch and get set to pull
your sound through the prism and into a spectrum of dazzling tones and timbres.

Sincerely,

The Elektron Team

Digitone II User Manual. This manual is copyright © 2024 Elektron Music Machines MAV AB. All reproduction, digital or
printed, without written authorization is strictly prohibited. The information in this manual may change without notice.
Elektron’s product names, logotypes, titles, words or phrases may be registered and protected by Swedish and interna-
tional law. All other brand or product names are trademarks or registered trademarks of their respective holders.
This manual for Digitone II OS version 1.00A was last updated October 23, 2024.


## 3. PANEL LAYOUT AND CONNECTORS

3. PANEL LAYOUT AND CONNECTORS

### 3.1 FRONT PANEL

```
TRIG SYN FLTR AMP FX MOD
```
```
FUNC
```
```
TRK
```
```
PTN
```
```
SONG
```
```
YES
```
```
NO PAGE
```
```
25
```
```
24
23
22
21
20
19
```
```
18
```
```
17
```
```
16
```
```
1 2 3 4 5 6 7 8
```
```
9
```
```
10
11
12
```
```
14
```
```
13
```
```
15
```
```
26
```
1. MAIN VOLUME sets the volume for the main outputs and the headphones output.
2. [PRESET/KIT] opens PRESET/KIT menu where you manage presets and kits. The secondary function
    accesses the PERFORM KIT mode.
3. [SETTINGS] opens the SETTINGS menu that contains the management of projects, MIDI configuration,
    and the System settings. The secondary function saves the current project.
4. [VOICE SETUP] opens the VOICE menu where you handle the track’s voice allocation.
    The secondary function toggles unison on/off.
5. [TEMPO] opens the TEMPO menu where you can adjust the global/pattern tempo and also adjust the
    swing. The secondary function makes it possible to tap the tempo.
6. [NO] key. Used for exiting an active menu, backing one step and negating. The secondary function is a
    reload from the temporary save of the active pattern.
7. [YES] key. Used for entering sub-menus, selecting and confirming. The secondary function is a tempo-
    rary save of the active pattern.
8. DATA ENTRY knobs A-H. Used for setting parameter values. Press and turn the knobs to change values
    in larger increments.
9. [PARAMETER] keys access the PARAMETER pages of the active track. The color of the keys indicates
    if the page is active (red/turquoise) or inactive (off).
    - [TRIG PARAMETERS] accesses parameters such as NOTE, VELOCITY, and other trig related
    parameters. The secondary function accesses the PRESET SETUP menu.
    - [SYN] accesses the SYN pages. Here you can find parameters related to the SYN machines. For MIDI
    tracks this page has parameters such as CHANNEL, PROGRAM, and AFTERTOUCH.
    - [FLTR] accesses the FILTER pages. Here are the parameters for the base-width and multimode filters.
    On MIDI tracks you find the CC value and select settings for track 1–8 here. The secondary function
    accesses the SETUP menu..


##### 3. PANEL LAYOUT AND CONNECTORS

- [AMP] takes you to the AMP page, where you find parameters for the amplitude envelope and effect
sends. On MIDI tracks you find the CC value and select settings for track 1–8 here. The secondary func-
tion access the parameters for the Euclidean sequencer.
- [FX] takes you to the FX pages, where you find parameters for the bit reduction, overdrive, sample rate
reduction, and effect sends levels. The secondary page accesses the parameters for the send effects.
- [MOD] accesses the LFO parameters for the active track. The secondary function accesses the
MIXER pages.
10. The <PATTERN PAGE> LEDs indicate how many pattern pages the active pattern consists of and which
pattern page is currently active. The LED flashes on the pattern page currently playing.
11. The [ARROW] keys. Used for navigation and for setting parameter values. In menus, they are called
[UP], [DOWN], [LEFT], and [RIGHT].
12. [PAGE] selects the active pattern page, if the pattern has more than 16 steps. The secondary function
accesses the SCALE menu. This key also activates FILL mode (when GRID RECORDING mode is not
active).
13. [ARPEGGIATOR] opens the ARPEGGIATOR menu. The secondary function toggles the arpeggiator
on/off.
14. [+]/[-] are used for transposing patterns, tracks and trigs. The secondary function transposes the active
track.
15. [NOTE EDIT] opens the NOTE EDIT menu where you can view, edit and add notes and change note relat-
ed settings such as note value, length, velocity and timing.
16. [TRIG] keys are used for entering or removing sequencer trigs, and parameter locks, in combination
with the DATA ENTRY knobs. They are also used to select a track, bank, pattern, and song in combina-
tion with the [TRK], [PTN], and [SONG] keys. The [TRIG] keys are also used as a keyboard in KEY-
BOARD mode. The secondary function is to Quick Mute tracks.
The [TRIG] keys lights indicate trigs on the sequencer by lit red keys, while flashing red or yellow keys
indicates parameter locks, in GRID RECORDING and STEP RECORDING mode. When a pattern is play-
ing, or when LIVE RECORDING is enabled, a light “runs” along the 16 steps of the sequencer across all
(up to eight) pages at the set tempo.
17. [SONG] selects song 1–16 in combination with the [TRIG 1–16] keys. The secondary function accesses
the Song edit screen.
18. [PTN] selects the bank and pattern in combination with the [LEFT]/[RIGHT] and [TRIG 1–16] keys. The
secondary function opens Bank selection.
19. [TRK] key. Press [TRK] + one of the [TRIG] keys to select a track for editing. The secondary function
accesses the MUTE mode.
20. [STOP] stops playback. The secondary function is a paste operation.
21. [PLAY] starts the sequencer playback. Pressing [PLAY] a second time pauses playback. The secondary
function is a clear operation.
22. [RECORD] key. Activates/deactivates GRID RECORDING mode. Press [RECORD] + [PLAY] and then
to activate LIVE RECORDING mode. [RECORD] + [STOP], to activate STEP RECORDING mode. The
secondary function is a copy operation.
23. [KEYBOARD] toggles KEYBOARD mode on/off. The secondary function opens the KEYBOARD SETUP
menu where you can view and edit settings such as scale, root note, and keyboard fold.
24. [FUNC] key. Press and hold [FUNC], and then press another key to access the secondary function of
that key. The turquoise text on the Digitone II front panel shows the keys secondary functions.
25. LEVEL/DATA sets the overall volume level of the active track. It is also used for setting parameters and
scrolling through lists. The secondary function opens the PRESET MANAGER. This knob is also used to
open the PRESET POOL when adding preset locks.
26. Screen.


##### 3. PANEL LAYOUT AND CONNECTORS

### 3.2 REAR CONNECTORS.

```
1 2 3 4 5 6 7 8 9
```
1. POWER Switch for turning the unit on and off.
2. DC In Input for power supply. Use the included PSU-3c power adapter, connected to a power outlet.
3. USB For connecting the unit to a computer. For MIDI-control or Overbridge use. Use the included A to B
    USB 2.0 connector cable to connect to a computer host.
4. MIDI THRU/SYNC B, Forwards data from MIDI IN. Can also be configured to send DIN sync to legacy
    instruments. Use a standard MIDI cable to connect another MIDI device in the chain.
5. MIDI OUT/SYNC A, MIDI data output. Can also be configured to send DIN sync to legacy instruments.
    Use a standard MIDI cable to connect to MIDI In of an external MIDI device.
6. MIDI IN, MIDI data input. Use a standard MIDI cable to connect to MIDI Out of an external MIDI device.
7. INPUT L/R, Audio inputs used for external sources for audio processing. Use either a 1/4” (Tip/Ring/
    Sleeve) phone plug (balanced connection) or a 1/4” mono phone plug (unbalanced connection).
8. OUTPUT L/R, Main audio outputs. Use either a 1/4” (Tip/Ring/Sleeve) phone plug (balanced connection)
    or a 1/4” mono phone plug (unbalanced connection).
9. HEADPHONES, Audio output for stereo headphones. Use 1/4” Tip/Ring/Sleeve phone plug.

### 3.3 SETTING UP AND STARTING THE DIGITONE II

```
Make sure you place the Digitone II on a stable support, such as a sturdy table, with sufficient space for the
cables. Make sure to switch off all devices before you connect the Digitone II to other devices.
```
1. Plug the supplied DC adapter into a power outlet and connect the small plug to the 12 V DC In on the
    Digitone II.
2. Connect OUTPUT L/R from the Digitone II to your mixer or amplifier. or use headphones plugged in to
    the HEADPHONES jack.
3. To control the Digitone II from a computer, connect a USB cable between the computer and the USB
    connector of the Digitone II.
4. If you want to use MIDI to control the Digitone II, connect the MIDI OUT port of the device you wish to
    send data from to the MIDI IN port of the Digitone II. The MIDI THRU port duplicates the data arriving at
    the MIDI IN port so that it can forward data to other MIDI units. If you want to use Digitone II to control
    other devices, connect the MIDI OUT port of the Digitone II to the MIDI IN port of the of the device you
    want to control.
5. Connect an audio source to INPUT L/R or via USB if you want to process audio from external sources.
6. Switch on all units. Press the POWER switch located at the back of the Digitone II to switch it on.


## 4. DIGITONE II SOUND ARCHITECTURE

4. DIGITONE II SOUND ARCHITECTURE

```
The illustrations below show the Digitone II internal sound architecture for its 16 audio voices.
```
### 4.1 AUDIO VOICES

```
SYN
MACHINE
```
```
BIT
REDUCTION AMP
```
```
FILTER
ENVELOPE
```
```
AMP
ENVELOPE
```
```
MIXER
```
```
DELAY RETURNS
```
```
FROM AUDIO VOICES OUTPUT L/R
HEADPHONES
```
```
FADE
ENVELOPE
```
```
LFO DESTINATION
```
```
PAN
```
```
DELAY
SEND
```
```
REVERB
SEND
```
```
TO MIXER
```
```
DELAY
SEND
```
```
CHORUS
```
```
DELAY
```
```
FROM EFFECT SENDS
```
```
TO MIXER
```
```
TO MIXER
```
```
REVERB RETURNS
```
```
CHORUS
SEND
```
```
FROM INPUT L/R
```
```
MASTER
OVERDRIVE
```
```
CHORUS RETURNS
```
```
REVERB
SEND
```
```
REVERB
SEND
```
```
REVERB TO MIXER
```
```
OVERDRIVE
SRR
B-W FILTER
```
```
OVERDRIVE
SRR
B-W FILTER
```
```
COMPRESSOR
```
```
FLTR
MACHINE
```
```
The overdrive, sample rate reduction, and the base-width filter can all individually be routed before or after
the filter machine.
```
### 4.2 EFFECTS

#### 4.2.1 SEND EFFECTS

```
SYN
MACHINE
```
```
BIT
REDUCTION AMP
```
```
FILTER
ENVELOPE
```
```
AMP
ENVELOPE
```
```
MIXER
```
```
DELAY RETURNS
```
```
FROM AUDIO VOICES OUTPUT L/R
HEADPHONES
```
```
FADE
ENVELOPE
```
```
LFO DESTINATION
```
```
PAN
```
```
DELAY
SEND
```
```
REVERB
SEND
```
```
TO MIXER
```
```
DELAY
SEND
```
```
CHORUS
```
```
DELAY
```
```
FROM EFFECT SENDS
```
```
TO MIXER
```
```
TO MIXER
```
```
REVERB RETURNS
```
```
CHORUS
SEND
```
```
FROM INPUT L/R
```
```
MASTER
OVERDRIVE
```
```
CHORUS RETURNS
```
```
REVERB
SEND
```
```
REVERB
SEND
```
```
REVERB TO MIXER
```
```
OVERDRIVE
SRR
B-W FILTER
```
```
OVERDRIVE
SRR
B-W FILTER
```
```
COMPRESSOR
```
```
FLTR
MACHINE
```
#### 4.2.2 MASTER EFFECTS

SYN
MACHINE

```
BIT
REDUCTION AMP
```
```
FILTER
ENVELOPE
```
```
AMP
ENVELOPE
```
```
MIXER
```
```
DELAY RETURNS
```
```
FROM AUDIO VOICES OUTPUT L/R
HEADPHONES
```
```
FADE
ENVELOPE
```
```
LFO DESTINATION
```
```
PAN
```
```
DELAY
SEND
```
```
REVERB
SEND
```
```
TO MIXER
```
```
DELAY
SEND
```
```
CHORUS
```
```
DELAY
```
```
FROM EFFECT SENDS
```
```
TO MIXER
```
```
TO MIXER
```
```
REVERB RETURNS
```
```
CHORUS
SEND
```
```
FROM INPUT L/R
```
```
MASTER
OVERDRIVE
```
```
CHORUS RETURNS
```
```
REVERB
SEND
```
```
REVERB
SEND
```
```
REVERB TO MIXER
```
```
OVERDRIVE
SRR
B-W FILTER
```
```
OVERDRIVE
SRR
B-W FILTER
```
```
COMPRESSOR
```
```
FLTR
MACHINE
```

## 5. OVERVIEW OF THE DIGITONE II DATA STRUCTURE

5. OVERVIEW OF THE DIGITONE II DATA STRUCTURE

```
The illustration below outlines the Digitone II’s data structure.
```
```
128 PATTERNS
1 Kit per pattern
```
```
16 AUDIO/MIDI TRACKS
(per pattern)
```
PROJECT

```
POOL
128 Presets
```
```
+DRIVE
Projects, Kits, Presets
```
16 SONGS

### 5.1 +DRIVE

```
The +Drive is a non-volatile RAM memory capable of storing up to 128 projects. The +Drive also holds
+Drive preset library, with the capacity of storing 2048 presets. Every project has access to these presets.
```
### 5.2 DATA STRUCTURE

#### 5.2.1 PROJECT

```
A project contains 128 patterns. The project also stores general settings and states. The currently loaded
project becomes the active working state of the Digitone II. From here it is possible to edit the patterns
and presets of the project. Every time the Digitone II is switched on, it boots to the active working state,
the active project. Projects are saved, loaded and managed in the SETTINGS menu. For more informa-
tion, please see “13. SETTINGS MENU” on page 70.
```
#### 5.2.2 PATTERNS

```
The patterns are the primary data container for the Digitone II. 16 patterns are available for each of the
eight banks, which means that 128 patterns are available for each project. A pattern contains up to 16
presets (one for each synth track), sequencer data like trigs and parameter locks. It also contains the
settings on the TRIG page and BPM, length, swing and time signature settings. The pattern also contains
all the parameter settings for the four MIDI tracks. For more information, please see “10. THE SEQUENC-
ER” on page 39.
```
#### 5.2.3 PRESETS

```
A preset is a collection of the synth track settings in the SYN1, SYN2, FLTR, AMP, and LFO PARAMETER
pages. Presets can be stored either in the preset pool of the active project or the +Drive preset library.
The preset pool holds up to 128 presets and the +Drive library holds up to 2048 presets. You can use
the PRESET MANAGER to manage sounds. For more information, please see “9. PATTERNS, KITS AND
PRESETS” on page 28.
```
```
A preset imported to a pattern, becomes an independent copy of the preset on the +Drive
and is not linked to the original preset on the +Drive. Instead, it becomes a part of the
pattern.
```
### 5.3 ABOUT THE TRACKS

```
The Digitone II sequencer has 16 tracks that can be either an audio track or a MIDI track.
```
#### 5.3.1 AUDIO TRACKS AND MACHINES

```
Any of the sixteen tracks can be used as an audio track. This is the default track setting. Each synth
track contains one preset. The preset contains the settings in the PARAMETER pages (SYN, FLTR, AMP,
FX, and MOD) together with the settings in the PRESET SETUP menu and the ARPEGGIATOR menu. A
track that contains any other SYN machine than the MIDI machine is considered an audio track.
```

##### 5. OVERVIEW OF THE DIGITONE II DATA STRUCTURE

A machine is a module within the Digitone II with specific functionality. A machine can be switched out
for another machine in the same category. For example different audio synthesis playback engines (SYN
machines) or a selection of filters (FLTR machines). Every machine has a specific set of parameters
tailored to give you the most relevant and useful sound-shaping possibilities for that particular machine.
For more information, please see “APPENDIX A: MACHINES” on page 87.

#### 5.3.2 MIDI TRACKS

Any of the 16 tracks can be used as a MIDI track. They are used to control external, MIDI equipped, gear.
Each MIDI track can trigger a chord of up to four notes with adjustable parameters such as velocity
and length, control pitch bend and aftertouch, as well as sixteen freely assignable MIDI control change
parameters (MIDI CCs). For more information, please see “A.2.5 MIDI” on page 97. Any MIDI channel
can be assigned to a MIDI track and several tracks can share the same channel. If several tracks are
assigned to the same MIDI channel the track with the lowest number has priority in case of parameter
conflicts.

The MIDI tracks function almost the same way as the audio tracks. Parameter locks, LFO modulation,
copy and paste commands are available. Each MIDI track also features micro timing, individual track
length and time signature settings. The main difference is that the MIDI tracks do not generate any sound
and the sequencer data is instead transmitted through the MIDI OUT or USB ports.

To use a track as a MIDI track, you must first assign a MIDI machine to it.

1. Press [FUNC] + [SYN] to open the MACHINE menu.
2. Use [LEFT]/[RIGHT] to navigate to the SYN machine category.
3. Use [UP]/[DOWN] to select the MIDI machine, and then press [YES] to assign it to the track.

#### 5.3.3 EDITING THE TRACKS

The six [PARAMETER] keys open the corresponding parameter page group which is where you edit the
tracks.

- The TRIG page contains different parameters such as NOTE, VELOCITY, and other trig related pa-
    rameters.
- The SYN page hosts the parameters that controls various parameters of the synthesis engines. For
    MIDI tracks, this page contains parameters such as CHANNEL, PROGRAM, and AFTERTOUCH..
- On the FLTR page, you find parameters for the selected filter/EQ machine and the base-width filter on
    audio tracks. On MIDI tracks you find the CC value settings and the CC select settings for CC param-
    eters 1–8 here.
- The AMP page for audio tracks hosts parameters for the amplitude envelope. On MIDI tracks you find
    the CC value settings and the CC select settings for CC parameters 9–16 here.
- The FX page contains the Bit Reduction, Overdrive, Sample Rate Reduction, and the send effects send
    levels for the audio tracks, The FX page is empty for MIDI tracks.
- The MOD page hosts LFO parameters for the active track.

Use the DATA ENTRY knobs A-H to edit the corresponding parameters. Press and turn a knob to adjust
its parameter in larger increments. Use the [UP]/[DOWN] keys to cycle through the parameter group's
pages. You can also press a [PARAMETER] key repeatedly to cycle trough the parameter pages in that
group. Press and hold a [PARAMETER] key to see the values for all parameters on that page. For more
information, please see “11. TRACK PARAMETERS” on page 55, and “APPENDIX A: MACHINES” on
page 87.


## 6. INTERACTING WITH THE DIGITONE II

6. INTERACTING WITH THE DIGITONE II

```
The screen shows all the information needed for real-time interaction and editing on the Digitone II. The
eight DATA ENTRY knob parameters shown will vary depending on the given situation. Below is the main
interface screen of the MOD page.
```
```
1 2 4
```
```
6
```
```
7
```
```
8
```
```
9
5
```
```
3
```
1. The current bank and pattern.
2. The current pattern name.
3. The selected TRIG mode.
4. The current tempo.
5. Eight track parameters. They show what the DATA ENTRY knobs control and their current parameter
    values.
6. Parameter page indicator. Indicates the number of pages for the selected parameter section and which
    page is currently selected.
7. The main level setting of the active track. Use the LEVEL/DATA knob to change the volume setting.
8. The current active track.
9. Track type. Audio (SYN) track or MIDI (MID) track.

```
The Digitone II features a screen saver that dims the screen after 5 minutes of inactivity
and turns off the screen after 60 minutes. Press any key or move any controller to wake
the screen.
```
### 6.1 SCREEN NAVIGATION

```
Use the [ARROW] keys [UP], [DOWN], [LEFT] or [RIGHT] to navigate menus or sub-menus.
The LEVEL/DATA knob can be used to scroll through menus and lists quickly.
[YES] is used to affirm, select, enter sub-menus and tick/untick boxes.
[NO] is used to negate, deselect or go back one or more steps.
```
```
When in a menu or sub-menu, the [NO] key can be used to go back, one step at a time, all the
way to the main screen.
```
### 6.2 PARAMETER EDITING

```
The DATA ENTRY knobs are used to change the values of the track parameters. The positions of the pa-
rameters on the screen correspond to the physical locations of the knobs on the front panel. Some of the
parameters on the screen tell you what DATA ENTRY knob controls that particular parameter.
For example “(E)” in the PAGE SETUP menu.
```
- The parameters are adjusted in larger increments if you press down the DATA ENTRY knob while turning
    it. This makes it quicker to sweep through the whole parameter range.
- Press DATA ENTRY knob + [NO] to reset the parameter to the default value.
- Press [PARAMETER] key + [PLAY] to reset all the parameters in the selected parameter page to
    default values.
- Press and hold a [PARAMETER] key to see the values for all parameters on that page.


##### 6. INTERACTING WITH THE DIGITONE II

#### 6.2.1 PARAMETER VALUE JUMP

```
Pressing [FUNC] while editing certain parameters will make the parameter values jump to appropriate
positions. The NOTE parameter, for example jumps between octaves and the delay time jumps between
BPM related tempo settings.
```
#### 6.2.2 CONTROL ALL

```
If you press and hold [TRK] and change a parameter setting, this change will affect this parameter in all
the audio tracks in the pattern. Press [NO] before you release [TRK] to revert the parameter changes.
In the SETUP menu, you have the possibility to select which tracks should be affected by the control all
functionality. For more information, please see “9.6.1 KIT” on page 34.
```
- Control all operations also affects the active track, whether it is selected to be affected
    or not.
- The control all functionality is not available for the MIDI tracks.

#### 6.2.3 [FUNC] KEY PRESS COMBINATIONS

```
The standard way to use the [FUNC] key in combination with other keys, is to press and hold [FUNC]
and then press the second key in the combination.
```
### 6.3 QUICK SCROLLING

Scroll through menus using the LEVEL/DATA knob. Quick scrolling is possible on many menus. Press
[FUNC] + the [UP] or [DOWN] keys to move the cursor one menu page.

### 6.4 COPY, CLEAR, AND PASTE

Copy, clear and paste commands are available in a lot of contexts. Pressing [FUNC] + [RECORD] to copy.
Press [FUNC] + [STOP] to paste. Press [FUNC] + [PLAY] to clear. Paste and clear operations are undone
by repeating the key press combination. Please see the different sections in the manual for more informa-
tion on when these commands are available. For more information, please see “16. KEY COMBINATIONS”
on page 82.

```
The copy clipboard can only hold one item at a time. When you perform a copy command,
the item copied replaces any earlier copied items. For example, you can not have both a
trig and a pattern copied at the same time.
```
### 6.5 THE NAMING SCREEN

The naming method is identical for the various naming situations that appear when you save presets, proj-
ects et cetera.

The [LEFT] and [RIGHT] arrow keys are used to navigate between the characters. Turning the LEVEL/
DATA knob or pressing the [UP] or [DOWN] arrow keys selects the characters. [FUNC] + [NO] erases
characters. [FUNC] + [YES] inserts space. Press [SETTINGS] to create a random name. Press and hold
[FUNC] to access the Pop-up Naming menu.

#### 6.5.1 POP-UP NAMING

```
A convenient way of naming is to open a pop-up menu that shows all available letters, symbols, and
digits. Press and hold the [FUNC] key when you are on the NAMING screen to access the Pop-up
Naming screen.
```

##### 6. INTERACTING WITH THE DIGITONE II

```
Keep [FUNC] pressed and use the [ARROW] keys to highlight the character you want to insert. Once
there, release [FUNC] to add the character.
```
```
Copy, paste, and clear commands are available on the NAMING screen.
```
### 6.6 OVERBRIDGE

```
The Overbridge software enables a tight integration between the Digitone II and a computer DAW software.
When using Overbridge, the user interface for the Digitone II presents itself as a plug-in window in your
DAW. Access, edit and automate parameters for sound shaping on screen. Always find your device preset
parameters in the same state as you left them when you return to your DAW project, with the useful total
recall functionality.
Read more about Overbridge use and availability on the Elektron website: https://www.elektron.se/overbridge/
```
```
Overbridge for Digitone II is still under development and is not currently available.
```
### 6.7 CLASS COMPLIANT DEVICE

```
The Digitone II is a class compliant device (also known as plug-and-play). It means it does not require any
extra drivers to connect to your computer or other USB class compliant hosts.
The Digitone II can, therefore, stream audio and MIDI directly over USB to and from supported computers/
phones/tablets. It opens up several exciting possibilities of what you can do with your device, for example,
record audio from your device directly over USB in your DAW. For more information, please see “13.8.1 USB
CONFIG” on page 77.
```
### 6.8 BACKING UP THE DIGITONE II

```
It is always recommended to make regular backups of your data. Backups are also a convenient way to
share your projects and presets with other users. To make backups and transfer files to and from your Elek-
tron device, you should use Transfer, a free application that you can download from the Elektron site https://
http://www.elektron.se/support. The application is available for both Windows and macOS.
```
#### 6.8.1 BACKING UP PROJECTS, PRESETS

```
You can backup your projects and presets from your Elektron device to a computer. Here is the general
procedure for how to back up your device:
```
1. Connect the Elektron device to the computer via USB.
2. Open the Transfer application on your computer, and then select the USB MIDI port(s) for your
    device on the CONNECTION page, and then click “CONNECT” next to your device under AVAIL-
    ABLE DEVICES.
3. In Transfer, click the EXPLORE tab to open the EXPLORE page.
4. On the EXPLORE page top left side of the drop-down menu, make sure “MY COMPUTER” is se-
    lected.
5. On the top right side, in the drop-down menu, select the type of file you want to transfer.
6. In the window on the right, navigate to the files or folders you want to transfer
7. Drag and drop the files or folders from your device to your preferred location under “MY COMPUT-
    ER.”


##### 6. INTERACTING WITH THE DIGITONE II

#### 6.8.2 TRANSFERRING BACKED UP FILES TO YOUR ELEKTRON DEVICE

Use the EXPLORE page in Transfer to transfer previously backed up files to your Elektron device from a
computer.

1. Connect the Elektron device to the computer via USB.
2. Open the Transfer application on your computer and then select the USB MIDI port(s) for your device
    on the Transfer CONNECTION page. Click “CONNECT” next to your device under AVAILABLE
    DEVICES.
3. In Transfer, click the EXPLORE tab to open the EXPLORE page.
4. On the top right side, in the drop-down menu, select the type of file you want to transfer.
5. On the top left side in the drop-down menu, make sure “MY COMPUTER” is
    selected.
6. Under “MY COMPUTER,” navigate to the files or folders you want to transfer.
7. Drag and drop the files or folders to your preferred location on your device.
8. Depending on what file type you send, you might encounter pop-up windows asking you to decide
    what files you want to send and how you want to organize them.

```
It is possible to import Digitone I projects to Digitone II. However, due to changes in the de-
vice architecture, the patterns in the projects might not be 100% the same in some cases.
```

## 7. QUICK START

7. QUICK START

```
This quick start will guide you through some of the basic operations to start using the Digitone II right away.
First, connect it as described in section “3.3 SETTING UP AND STARTING THE DIGITONE II” on page 14.
```
### 7.1 SELECTING AND PLAYING THE PRESETS

```
There are a large number of presets in the Digitone II that you can use in your music-making or as starting
points for your own sound design.
```
1. Press [PRESET/KIT] to open the PRESET/KIT menu.
2. Select LOAD (PRESET) to open the LOAD PRESET MENU
3. Use the [UP]/[DOWN] keys or LEVEL/DATA to highlight a preset.
4. Press [KEYBOARD] and then use the [TRIG] keys to play and listen the highlighted preset.
5. Press [YES] to load the preset to the active track.

### 7.2 PLAYING THE FACTORY PRESET PATTERNS.

```
You can find a number of factory preset patterns in the Digitone II. Follow the instructions below to get
started exploring your new instrument.
```
1. Press [PTN] and then use the [LEFT]/[RIGHT] keys to select bank A. Then press [TRIG 1] to select
    pattern A01.
2. Press [PLAY] to listen to pattern A01.
3. Press [PTN], and then press [TRIG 2] to cue pattern A02. It will start once pattern A01 has reached
    its end. Select pattern A03 by pressing [PTN], and then press [TRIG 3], and so on.
4. Press [STOP] to stop playback.

### 7.3 USING KEYBOARD MODE

```
You can use the [TRIG] keys to play any track chromatically.
```
1. Select the track to play chromatically by pressing [TRK] + one of the [TRIG 1–16] keys.
2. Press [KEYBOARD] to enter KEYBOARD mode. The [TRIG] keys will light up in a pattern that resem-
    bles an octave of a piano keyboard. Only lit keys are playable.
3. Play the [TRIG] keys. The preset will be pitched differently for each of the playable keys. Press [+]/[-]
    to transpose the virtual keyboard up or down one octave.
For more information, please see “8.5.1 KEYBOARD MODE” on page 24.

```
KEYBOARD mode is an effective way to add musical variety to your tracks. The timbre,
tonality and impact of playing a preset chromatically depends on the character of the source
preset.
```
### 7.4 USING MUTE MODE

```
You can mute any of the sequencer tracks in this mode. Unlike the KEYBOARD mode, it makes no
difference which track is active when this mode is activated. You can access all tracks simultaneously.
```
1. Make sure a pattern is playing.
2. Press the [FUNC] + [TRK] key to enter MUTE mode.
3. Press any of the [TRIG] keys to mute the corresponding track. Press again to unmute. The light of the
    [TRIG] keys indicates the mute status. Unlit keys are muted tracks. Lit keys are active tracks.
For more information, please see “8.5.3 MUTE MODE” on page 25.


##### 7. QUICK START

### 7.5 TEMPO AND METRONOME

Press the [TEMPO] key to open the TEMPO menu.

#### 7.5.1 TEMPO

```
To change the BPM setting, open the TEMPO menu by pressing the [TEMPO] key. Use DATA ENTRY
knob A or LEVEL/DATA to change tempo. Pressing the knob while turning it changes the tempo four
BPM at a time. Use DATA ENTRY knob B or [UP] or [DOWN] to change the tempo in decimal steps. You
can chose between a tempo for the whole project or use a separate tempo every pattern. Press and
hold [FUNC], and then use DATA ENTRY knob E to switch between these modes. On the main interface
screen, you can also press and hold the [ARROW] keys [LEFT] or [RIGHT] to nudge the tempo 10% up
or down temporarily. Release the key to revert to the original tempo.
To tap a tempo setting, hold [FUNC] and tap the [TEMPO] key in a steady rhythm. After four consecutive
taps the average tempo of the taps will be calculated. By continuing tapping, the average tempo will keep
on updating.
When in the TEMPO menu (and with PROJECT tempo mode selected), holding [FUNC] while turning
DATA ENTRY knob A will not change the tempo to the selected value until [FUNC] is released. While
doing this, “PREP.” flash in the bottom left corner of the screen.
```
```
Nudging the tempo is very handy when manually syncing Digitone II to a turntable or an
external sound source. Note that you do not need to be in the TEMPO menu to nudge the
tempo.
```
```
Use DATA ENTRY knob D to adjust the SWING setting of the pattern, to employ a propulsive, rhythmic
groove. The swing ratio can be set to 51-80%. The default setting is equal spacing, 50%.
```
#### 7.5.2 METRONOME

```
The metronome settings controls the internal metronome of the Digitone II. Use the DATA ENTRY knobs
to change the settings.
```
```
METRO activates/deactivates the metronome.
SIG. controls the note and beat measure of the metronome time signature.
PREROLL controls for how many bars the metronome will sound before the sequencer starts playing.
This setting is only relevant when you are in LIVE RECORDING mode.
VOL controls the volume of the metronome click.
```
### 7.6 EDITING PARAMETERS

Each track has six PARAMETER page groups that each contain a number of pages. Press [PARAMETER]
keys TRIG, SYN, FLTR, AMP, FX, and MOD to access the different PARAMETER page groups. These param-
eters affect the sound and signal in various ways.

1. Make sure a pattern is playing.
2. Press [TRK] + [TRIG] keys 1–16 to select a track.
3. To change, for example, the cutoff of the filter, press the [FLTR] key to open the FILTER page.
    The parameter labeled FREQ changes the cutoff of the filter. Turn DATA ENTRY knob E to change
    the parameter value, and hear how the sound is affected.

Try out the rest of the PARAMETER page parameters to explore a variety of sound shaping possibilities.


## 8. DIGITONE II CONTROLS TABLE OF CONTENTS

8. DIGITONE II CONTROLS

### 8.1 TRIG KEYS

```
The [TRIG] keys have several uses, including for example, triggering an audio/MIDI track from the active
pattern. They can also be used for placing trigs in the RECORDING modes. When pressed in combination
with the [PTN] and [SONG] keys, they select patterns and songs. The [TRIG] keys light up to indicate the
position of placed trigs and to indicate the selected bank and track.
```
### 8.2 ROTARY ENCODERS

```
The MAIN VOLUME is an absolute encoder, spanning roughly 320 degrees from its left extreme to its right
extreme. The LEVEL/DATA and DATA ENTRY knobs (with which you set various parameter values), are
relative encoders which may be spun any number of turns. Pressing and turning these encoders will change
their associated values at a greater speed.
```
### 8.3 KEY BEHAVIOR

```
As a group, the track selection keys (the [TRIG] keys) have radio button functionality, i.e. when a new track
is set to be active, the previous one is simultaneously deactivated. Only one track can be selected at a time.
Likewise, the group consisting of the six [PARAMETER] keys also have radio button functionality.
```
### 8.4 MIDI NOTES

```
Some functions can be triggered by sending MIDI note values from an external MIDI device (a MIDI key-
board or a computer, for example) connected to the Digitone II via a standard MIDI cable or a USB 2.0 A to
B connector cable.
MIDI note numbers 0–127 (corresponding to notes C0–G10, the first through to eleventh octaves in the MIDI
range) triggers the preset of the active track.
MIDI program change messages 0–127 will select pattern 1–128 (A01–H16) on the Digitone II. Additionally,
MIDI CC and NRPN messages can be sent to control various aspects of the Digitone II. For more informa-
tion, please see “APPENDIX C: MIDI” on page 111.
```
### 8.5 MODES

```
There are several modes of operation on the Digitone II that decides what action the [TRIG] keys perform
when pressed.
```
#### 8.5.1 KEYBOARD MODE

```
Press [KEYBOARD] to enter KEYBOARD mode. In this mode, you can play the preset of the active audio
track (or send MIDI notes if you have an active MIDI track). With KB SCALE set to CHROMATIC and
MODE set to REGULAR the [TRIG] keys will light up in a pattern that resembles an octave of a piano
keyboard layout. Only lit keys are playable. Press [TRK] + [TRIG 1–16] keys to select the track you want
to play.
Press the lit trig keys to play different notes. The range from [TRIG 9] key to [TRIG 16] key is one octave.
The whole note range you can trig is a little more than 10 octaves (C0–G10) You can transpose the key-
board 4 octaves up and 5 octaves down from the middle octave. Press [+]/[−] to transpose the virtual
keyboard up or down in octave steps. With U/D KEY MODE set to KB OCT, you can also press [UP]/
[DOWN] to transpose the virtual keyboard when you are in KEYBOARD mode. For more information,
please see “13.7.4 U/D KEY MODE” on page 77.
Notes triggered can be recorded on the sequencer in LIVE RECORDING mode. Find out how this is done
in section “10.4 LIVE RECORDING MODE” on page 42.
Press [KEYBOARD] again to exit KEYBOARD mode.
```
```
In KEYBOARD mode the color of the [+]/[−] keys shows the current octave for the [TRIG]
keys. White: 1 octave up/down, Yellow: 2 octaves up/down, Orange: 3 octaves up/down,
Red: 4 octaves up/down, Dark Red: 5 octaves down.
```
#### 8.5.2 KEYBOARD SETUP MENU

```
Press [FUNC] + [KEYBOARD] to open the KEYBOARD SETUP menu. Here you get a graphical view of
which notes the [TRIG] keys play. Press [+]/[−] or [UP]/[DOWN] to transpose the keyboard up or down
an octave. In this menu, you can also set different musical scales, and select the root note of the scale.
Use the DATA ENTRY knobs to change the settings. Press [NO] to exit the menu. Press [KEYBOARD]
again to exit KEYBOARD mode. The settings are stored in the active pattern.
```

##### 8. DIGITONE II CONTROLS

KB SCALE sets the track’s scale. This setting governs which notes are playable on the Digitone II’s
[TRIG] keys to allow only notes in the set scale. For a list of all selectable keyboard scales, please see
”APPENDIX E: KEYBOARD SCALES” on page 118

ROOT NOTE sets the root note for the chosen scale.

KB FOLD When set to ON, KB FOLD changes the way the notes are laid out on the [TRIG] keys and how
they are played from external MIDI devices. Now all the [TRIG] keys trigger a note. The [TRIG 9] key
plays the lowest note of the selected octave, root note and scale. The notes are then played upwards
through the scale to [TRIG 16] and then further upwards on [TRIG 1–8]. When a scale contains less than
eight notes in an octave, the “remaining” [TRIG] keys on the lower row will play the same notes as the
first [TRIG] keys on the upper row. Blue [TRIG] keys signal that they contain notes that are one or more
octaves distanced from each other.

In the following example of the whole tone scale [TRIG 15–16] keys play the same notes as the [TRIG
1–2] keys. [TRIG 1] and [TRIG 15] both plays a note that is one octave above [TRIG 9]. [TRIG 7] plays a
note that is two octaves above [TRIG 9].

- The active state and settings of the KEYBOARD mode is stored per pattern.
- You can also use an external keyboard to play the active tracks preset chromatically.
    Connect the keyboard to the Digitone II and configure the external keyboard and the
    Digitone II MIDI Auto Channel (SETTINGS > MIDI CONFIG > CHANNELS) to the same
    MIDI channel. Then play the keys on the external keyboard to play the active tracks
    preset chromatically. You can play the active track’s preset chromatically even if the
    Digitone II is not in KEYBOARD mode.

#### 8.5.3 MUTE MODE

You can use MUTE mode to mute any of the 16 sequencer tracks. Unlike KEYBOARD mode,
it makes no difference which track is active when you enter MUTE mode. All tracks are accessed simul-
taneously. Press any of the [TRIG] keys to mute the corresponding track. Press again to unmute. The
color of the [TRIG] keys indicates it’s tracks mute status. Lit keys signify unmuted tracks. Unlit keys
signify muted tracks.

There are two different versions of MUTE mode on the Digitone II:

- GLOBAL MUTE MODE In GLOBAL MUTE mode, the muted tracks are muted in all patterns. In this
mode, the [TRACK] keys are lit green. If a track with trigs placed on it is muted in GLOBAL MUTE
mode, its [TRACK] key blinks green when the pattern plays.
Press [FUNC] + [TRK] to enter GLOBAL MUTE mode.
Press [FUNC] + [TRK] to exit GLOBAL MUTE mode.
- PATTERN MUTE MODE In PATTERN MUTE mode, the muted tracks are muted only in the active
pattern. In this mode, the [TRACK] keys are lit magenta. If a track with trigs placed on it is muted in
PATTERN MUTE mode, its [TRACK] key blinks magenta when the pattern plays.
Press [FUNC] + double-press [TRK] to enter PATTERN MUTE mode.
Press [FUNC] + [TRK] to exit PATTERN MUTE mode.

The GLOBAL MUTE mode settings are saved together with the project. The PATTERN MUTE mode
settings are saved together with the pattern.


##### 8. DIGITONE II CONTROLS

```
If a track with trigs placed on it is muted in PATTERN MUTE and GLOBAL MUTE mode, its [TRACK] key
blinks magenta in both modes when the pattern plays.
You can also prepare tracks for muting/unmuting before the mute action takes effect. While in any MUTE
mode, press and hold [FUNC] and then press the [TRIG] keys corresponding with the tracks you want
to mute/unmute. The selected tracks will then be muted/unmuted when you release [FUNC]. At the top
of the screen you can see an overview of the mute status for all the tracks. Square = unmuted track,
horizontal line = muted track, a plus sign = a track prepared to be unmuted, an X = track prepared to be
muted.
```
- You can also use QUICK MUTE to mute and unmute sequencer tracks. Press and hold
    [FUNC] and then press the [TRACK] keys to mute or unmute tracks.
- Digitone II remembers the last used version of MUTE mode and will access this one
    first when you press [FUNC] + [TRK]. This also applies to QUICK MUTE. However, after
    reboot, it defaults back to GLOBAL MUTE mode.

#### 8.5.4 TRIG MODE

```
The various TRIG modes affect the functionality of the [TRIG 1–16] keys. Press [FUNC] + [UP] or
[DOWN] to select which TRIG mode that should be active. The selected TRIG mode is active for all the
tracks. All actions performed using the trig modes can be recorded in LIVE RECORDING mode.
```
##### TRACKS

```
This is the default TRIG mode. In this mode, the [TRIG 1–16] keys triggers the corresponding audio/
MIDI tracks.
```
```
VELOCITY
In this TRIG mode, the [TRIG 1–16] keys triggers the active track with different amounts of velocity.
Trig 1 = Velocity 8
Trig 2 = Velocity 16
Trig 3 = Velocity 24
Trig 4 = Velocity 32
Trig 5 = Velocity 40
Trig 6 = Velocity 48
Trig 7 = Velocity 56
Trig 8 = Velocity 64
Trig 9 = Velocity 72
Trig 10 = Velocity 80
Trig 11 = Velocity 88
Trig 12 = Velocity 96
Trig 13 = Velocity 104
Trig 14 = Velocity 112
Trig 15 = Velocity 120
Trig 16 = Velocity 127
```
```
RETRIGS
In this TRIG mode, the [TRIG 1–16] keys triggers the active track with different amounts retrig rates.
Trig 1 = Retrig rate 1/1
Trig 2 = Retrig rate 1/2
```

##### 8. DIGITONE II CONTROLS

Trig 3 = Retrig rate 1/3

Trig 4 = Retrig rate 1/4

Trig 5 = Retrig rate 1/5

Trig 6 = Retrig rate 1/6

Trig 7 = Retrig rate 1/8

Trig 8 = Retrig rate 1/12

Trig 9 = Retrig rate 1/16

Trig 10 = Retrig rate 1/20

Trig 11 = Retrig rate 1/24

Trig 12 = Retrig rate 1/32

Trig 13 = Retrig rate 1/40

Trig 14 = Retrig rate 1/48

Trig 15 = Retrig rate 1/64

Trig 16 = Retrig rate 1/80

PRESET POOL

In this TRIG mode, the [TRIG 1–16] keys triggers sixteen preset slots in the preset pool. The first page
triggers the presets in slots 1–16. Press [PAGE] + [RIGHT] to go to page 2 to trigger the presets in
slots 17–32 and so on. Press [PAGE] + [LEFT/RIGHT] to select which slots you wish to trigger. For
more information, please see “9.1 THE +DRIVE LIBRARY AND THE POOL” on page 28.

```
Incoming MIDI note data are mapped to the tracks in different ways depending on the
chosen TRIG mode.
```
```
The RETRIGS and PRESET POOL trig modes are only supported on audio tracks.
```

## 9. PATTERNS, KITS AND PRESETS

9. PATTERNS, KITS AND PRESETS

```
The patterns are the primary data container for the Digitone II. Sixteen patterns are available for each of the
eight banks, which means that 128 patterns are available for each project. A pattern contains up to 16 pre-
sets (one for each track), sequencer data like trigs and parameter locks. It also contains the default settings
on the TRIG page and BPM, length, swing and time signature settings.
A kit is a collection of 16 presets, one for every track together with a number of kit related settings. A preset
is either an audio track preset or a MIDI track preset. Audio track presets contain all settings from the SYN,
FLTR, AMP, FX, and MOD PARAMETER pages, and MIDI track presets contain all settings from the SYN,
FLTR, AMP, and MOD PARAMETER pages. Any preset can be assigned to any of the 16 tracks.
A preset or kit that has been imported from the +Drive to a pattern becomes part of the active pattern. Any
changes made to a preset/kit will therefore not affect the stored preset/kit. It will only affect the preset/
kit in the active pattern. You can export (and in that way save) a preset/kit from the active pattern to the
+Drive. For more information, please see “9.2 PRESET/KIT MENU” on page 29.
```
```
When a preset or a kit is imported to a pattern, it becomes a copy of the preset/kit on the
+Drive and is not linked to the original preset/kit stored on the +Drive. Instead, it fully be-
comes a part of the pattern.
```
```
A pattern contains:
```
- A kit.
- Sequencer data such as trigs and parameter locks for the 16 tracks.
- The settings on the TRIG page, BPM, length, swing and time signature settings.

```
A kit contains:
```
- 16 audio or MIDI track presets.
- LEVEL settings for the audio tracks and the pattern.
- Compressor and Master distortion settings
- Compressor routing and Control all settings
- Send FX settings
- Track layering settings
- Patterns transpose settings

```
A preset contains:
```
- The SYN, FLTR, AMP, FX and MOD PARAMETER pages settings for the audio track. Or, in the case of
    MIDI presets, the SYN, FLTR, AMP, and MOD PARAMETER pages settings for the MIDI track.

### 9.1 THE +DRIVE LIBRARY AND THE POOL

```
The +Drive library can hold up to 2048 presets (256 presets in each bank A-H) that are available to all proj-
ects. The kits are also stored here.
Presets can be loaded to a pattern from either the +Drive library or the pool (Project RAM) of the active
project. The difference between the two is that the +Drive library has the capacity of 2048 presets, avail-
able to all projects, while the pool is a part of a projects internal memory and limited to 128 presets. The
primary benefit of presets loaded to the pool is the possibility for them to be preset locked. This feature is
not available for the presets in the +Drive library. For more information, please see “10.12.2 PRESET LOCKS”
on page 48.
```
#### 9.1.1 ADDING PRESETS TO THE POOL

```
You must first add presets to the pool to be able to perform preset locks.
```
1. Press [PRESET/KIT] to open the PRESET/KIT menu.
2. Select (PRESET) MANAGE, and then press [YES] to open the PRESET MANAGER.
3. Select the presets you want to add to the pool by highlighting them and pressing [YES].
4. Press [LEFT] to open the PRESET SORTING menu.
5. Select ADD TO PRESET POOL and then press [YES].

```
MIDI presets can not be added to the preset pool
```

##### 9. PATTERNS, KITS AND PRESETS

### 9.2 PRESET/KIT MENU

Press [PRESET/KIT] to open the PRESET/KIT menu. Here you manage presets (both on the +Drive and in
the pool) and kits. Use the [ARROW] keys and LEVEL/DATA to navigate the menu. Press [YES] to select a
menu item. Press [NO] to exit the menu.

#### 9.2.1 LOAD (PRESET)

```
Use the [ARROW] keys or LEVEL/DATA to select a preset. Press [YES] to load the preset to the active
track. Press [TRK] + [TRIG 1–16] to change active track. Press the tack’s [TRIG] key to preview the
highlighted preset before you load it. You can also press [FUNC] + [YES] to preview the preset. Press
KEYBOARD, and then use the [TRIG] keys to preview the selected preset in the selected scale.
Press [LEFT] to access the SORTING menu. Press [YES] to execute the commands. Press [NO] or the
[RIGHT] arrow key to exit the menu.
```
```
ADD TO PRESET POOL adds the selected preset to the pool of the active project.
SEL BANK (ALL) opens the Preset bank selector. The currently selected bank is shown in parenthe-
sis. Use the [TRIG 8–16] keys, the [ARROW] keys, or LEVEL/DATA to select bank.
SORT ABC / SORT 123 toggles between if the presets are sorted in alphabetical order or by slot
number.
FILTER opens a list where presets can be arranged according to tags. Select and deselect tags by
pressing [YES]. You can select multiple tags. Exit the tag list by pressing [NO].
SEARCH will perform a text search and list all presets with names matching or including the text input.
For more information, please see “6.5 THE NAMING SCREEN” on page 19 on how to enter text.
```
```
You can also press [FUNC] and then turn LEVEL/DATA to open the LOAD (PRESET) menu.
```
#### 9.2.2 SAVE (PRESET)

```
Use this option to save the active tracks preset.
```
1. Use the [UP]/[DOWN] keys or LEVEL/DATA to select a slot to where you want to save the preset.
2. Press [YES] to save the preset to the selected slot. If the slot already contains a preset you are
    asked to confirm the save operation.
3. On the NAMING screen, name your preset and then press [YES]. For more information, please see
    “6.5 THE NAMING SCREEN” on page 19.
4. On the TAG screen you can add tags to the preset. The first two will show on the preset list. Press
    [YES] to apply or remove tags. Highlight <SAVE> and press [YES] to save.

```
ADD TO PRESET POOL adds the selected preset to the pool of the active project.
SELECT BANK opens the Preset bank selector. The currently selected bank is shown in parenthesis.
Use the [TRIG 8–16] keys, the [ARROW] keys, or LEVEL/DATA to select bank.
SORT ABC / SORT 123 toggles between if the presets are sorted in alphabetical order or by slot number.
FILTER opens a list where presets can be arranged according to tags. Select and deselect tags by
pressing [YES]. You can select multiple tags. Exit the tag list by pressing [NO].
SEARCH will perform a text search and list all presets with names matching or including the text input.
For more information, please see “6.5 THE NAMING SCREEN” on page 19 on how to enter text.
SHOW ALL SLOTS will, when ticked, show all the preset slots both populated and empty. When not
ticked, it only shows empty slots.
```

##### 9. PATTERNS, KITS AND PRESETS

#### 9.2.3 MANAGE (PRESET)

```
Opens the Preset manager. Here presets can be saved, loaded, renamed, tagged et cetera. Opening this
menu will show a list of all presets found in the +Drive library.
```
```
Press [LEFT] to access the SORTING menu. Press [YES] to execute the commands. Press [NO] or the
[RIGHT] key to exit the menu.
```
```
ADD TO PRESET POOL adds the selected preset to the pool of the active project.
SELECT BANK opens the Preset bank selector. The currently selected bank is shown in parenthe-
sis. Presets in the +Drive library are organized into eight banks, ranging from A to H. Each bank can
contain 256 presets. Use the [TRIG 9–16] keys to view only presets located in a specific bank. Use the
[TRIG 8–16] keys, the [LEFT /[RIGHT] keys, or LEVEL/DATA to select bank.
SORT ABC / SORT 123 toggles between if the presets are sorted in alphabetical order or by slot
number.
FILTER opens a list where presets can be arranged according to tags. Select and deselect tags by
pressing [YES]. You can select multiple tags. Exit the tag list by pressing [NO].
SEARCH will perform a text search and list all presets with names matching or including the text input.
For more information, please see “6.5 THE NAMING SCREEN” on page 19 on how to enter text.
SHOW ALL SLOTS will, when ticked, show all the preset slots both populated and empty. When not
ticked, it only shows empty slots.
Press [RIGHT] to access the PRESET OPERATIONS menu. The available operations will affect the high-
lighted preset. Use LEVEL/DATA or the [UP]/[DOWN] arrow keys to navigate the menu. Press [YES] to
apply the commands to the selected preset. Press [NO] or the [LEFT] arrow key to exit the menu.
```
```
LOAD TO TRACK loads the selected preset to the active track and makes it a part of the active pattern.
COPY TO BANK copies the selected presets to the first free slots of a specific bank inside the +Drive.
SAVE TO HERE saves the active track preset to the selected slot.
RENAME Opens a screen where you can rename the selected preset.
EDIT TAGS opens a menu where presets can be tagged. presets can have any number of tags, but
only the first two will show on the preset list. Press [YES] to apply or remove tags. Highlight <SAVE>
and press [YES] to save.
```

##### 9. PATTERNS, KITS AND PRESETS

```
DELETE deletes the preset or selected presets.
SELECT ALL selects all presets in the list.
DESELECT ALL deselects all selected presets in the list.
TOGGLE enables or disables write protection for the selected presets. When a preset is write pro-
tected it cannot be overwritten, renamed, tagged or deleted. A write protected preset has a padlock
symbol next to its name. The command is only available when you browse the +Drive library.
SEND SYSEX sends the selected presets as SysEx data.
```
- It is also possible to save/load/manage MIDI track presets in the same way as you do with
    audio track presets. They will automatically be tagged with the MIDI tag when they are
    saved. It is not possible to load a MIDI preset to the preset pool.
- You can preview the currently selected preset by pressing the [TRIG 1–16] key of the active
    track. Please note that if the previewed preset is sent to the effects, the current effects
    settings will affect it.
- Several presets can be simultaneously affected by the commands available in the PRESET
    OPERATIONS menu. Select/deselect individual presets by highlighting them and pressing
    [YES].
- Press [FUNC] + [UP]/[DOWN] for faster scrolling in the preset list.

#### 9.2.4 POOL

Provides a quick access to the pool in the Preset manager. Here you can manage the preset pool. You
can, among other things add presets, select multiple presets, delete presets, and rename presets. For
more information about the preset pool, please see “9.1 THE +DRIVE LIBRARY AND THE POOL” on page
28.

Press [RIGHT] to access the PRESET OPERATIONS menu. The available operations will affect the high-
lighted preset. Use LEVEL/DATA or the [UP]/[DOWN] arrow keys to navigate the menu. Press [YES] to
apply the commands to the selected preset. Press [NO] or the [LEFT] arrow key to exit the menu.

```
LOAD TO TRACK loads the selected preset to the active track and makes it a part of the active pattern.
SAVE TO HERE saves the active track preset to the selected slot.
RENAME Opens a screen where you can rename the selected preset.
DELETE deletes the preset or selected presets.
SELECT ALL selects all presets in the list.
SELECT UNUSED selects all presets that are not used in any pattern in the project.
DESELECT ALL deselects all selected presets in the list.
```
#### 9.2.5 LOAD (KIT)

Use the [ARROW] keys or LEVEL/DATA to select a kit. Press [YES] to load the kit to the active pattern.
Press the [TRIG] keys to preview the highlighted kit before you load it.

Press [LEFT] to access the SORTING menu. Use LEVEL/DATA or the [UP]/[DOWN] arrow keys to
navigate the menu. Press [YES] to execute the commands. Press [NO] or the [RIGHT] arrow key to exit
the menu.


##### 9. PATTERNS, KITS AND PRESETS

```
ADD TO PRESET POOL adds the kit’s 16 presets to the pool of the active project.
SORT ABC / SORT 123 toggles between if the kits are sorted in alphabetical order or by slot number.
SELECT BANK opens the KIT bank selector. The currently selected bank is shown in parenthesis. Use
the [TRIG 8–16] keys, the [ARROW] keys, or LEVEL/DATA to select bank.
SEARCH will perform a text search and list all kits with names matching or including the text input. For
more information, please see “6.5 THE NAMING SCREEN” on page 19 on how to enter text.
```
#### 9.2.6 SAVE (KIT)

```
Use this option to save the active patterns kit.
```
1. Use the [UP]/[DOWN] keys or LEVEL/DATA to select a slot to where you want to save the kit.
2. Press [YES] to save the kit to the selected slot. If the slot already contains a kit you are asked to
    confirm the save operation.
3. On the NAMING screen, name your kit and then press [YES] to save. For more information, please
    see “6.5 THE NAMING SCREEN” on page 19.

```
Press [LEFT] to access the SORTING menu. Press [YES] to execute the commands. Press [NO] or the
[RIGHT] arrow key to exit the menu.
ADD TO POOL adds the kit’s 16 presets to the pool of the active project.
SORT ABC / SORT 123 toggles between if the kits are sorted in alphabetical order or by slot number.
SELECT BANK opens the KIT bank selector. The currently selected bank is shown in parenthesis. Use
the [TRIG 8–16] keys, the [ARROW] keys, or LEVEL/DATA to select bank.
SEARCH will perform a text search and list all kits with names matching or including the text input. For
more information, please see “6.5 THE NAMING SCREEN” on page 19 on how to enter text.
SHOW ALL SLOTS will, when ticked, show all the kit slots both populated and empty. When not ticked,
it only shows empty slots.
```
#### 9.2.7 MANAGE (KIT)

```
Kits can be saved, loaded, renamed, tagged et cetera. Opening this menu will show a list of all presets
found in the +Drive library.
```
```
Press [LEFT] to access the SORTING menu. Press [YES] to execute the commands. Press [NO] or the
[RIGHT] key to exit the menu.
```

##### 9. PATTERNS, KITS AND PRESETS

```
ADD TO POOL adds the kit’s 16 presets to the pool of the active project.
SORT ABC / SORT 123 toggles between if the kits are sorted in alphabetical order or by slot number.
SELECT BANK opens the KIT bank selector. The currently selected bank is shown in parenthesis. Use
the [TRIG 8–16] keys, the [ARROW] keys, or LEVEL/DATA to select bank.
SEARCH will perform a text search and list all kits with names matching or including the text input. For
more information, please see “6.5 THE NAMING SCREEN” on page 19 on how to enter text.
Press [RIGHT] to access the KIT OPERATIONS menu. The available operations will affect the highlighted
preset. Press [YES] to apply the commands to the selected kit. Press [NO] or [LEFT] arrow key to exit
the menu.
```
```
LOAD TO PATTERN loads the selected kit to the active pattern.
COPY TO BANK copies the selected kits to BANK (A-H) The kit is copied to the first free slot of a
specific bank inside the +Drive.
SAVE TO HERE saves the active kit to the selected slot.
LOAD TO EMPTY loads the selected kit to all empty patterns
RENAME opens a screen where you can rename the selected kit.
DELETE deletes the kit or selected kits.
SELECT ALL selects all kits in the list.
SELECT UNUSED selects all kits that are not used in any pattern in the project from the pool. The
presets are not removed from the +Drive library. Then select DELETE to remove the selected kits
from the project’s pool.
DESELECT ALL deselects all kits in the list.
TOGGLE enables or disables write protection for the selected kits. When a kit is write protected it
cannot be overwritten, renamed, tagged or deleted. A write protected kit has a padlock symbol next to
its name. The command is only available when you browse the +Drive library.
SEND SYSEX sends the selected kit's presets as SysEx data.
```
### 9.3 PLAYING A PRESET

Press the [TRIG 1–16] keys to play the presets of the tracks in the active pattern. The [TRIG] keys will brief-
ly light up when pressed.

#### 9.3.1 PLAYING A PRESET WITH AN EXTERNAL MIDI UNIT

```
The presets can also be played using an external MIDI device connected to Digitone II. The MIDI chan-
nels for of each of the tracks can be assigned in the MIDI CHANNELS menu, covered in the section
“13.4.3 CHANNELS” on page 74. You can also use an external MIDI unit to play a preset chromatically
when you are in KEYBOARD mode. For more information, please see “8.5.1 KEYBOARD MODE” on page
24.
```
### 9.4 EDITING A PRESET

Select a track for editing by pressing [TRK] + one of the [TRIG 1–16] keys. When editing the settings of the
track, any changes made will be stored as part of the active pattern.

Adjust the overall volume level of the active audio track with the LEVEL/DATA knob. This setting is saved as
part of the kit.

Edit a preset by adjusting the parameters found on the PARAMETER pages. Access these pages by
pressing a [PARAMETER] page key. Use the DATA ENTRY knobs A-H to change the parameters. For more
information, please see “11. TRACK PARAMETERS” on page 55.


##### 9. PATTERNS, KITS AND PRESETS

```
The complete preset, with all its parameter settings may be copied to another track. Press [TRK] + [TRIG
1–16] + [RECORD], and then press [TRK] + [TRIG 1–16] + [STOP] to paste the preset to the selected track.
```
### 9.5 SAVING A PRESET

```
After you edited the parameters of a preset, you can save it to the +Drive.
```
1. Press [PRESET/KIT] to open the PRESET/KIT menu.
2. Select PRESET > SAVE, and then press [YES].
3. Turn the LEVEL/DATA knob or use the [UP]/[DOWN] keys to select an empty slot to where you want
    to save your preset, and then press [YES].
4. On the NAMING screen, name your preset and then press [YES]. For more information, please see
    “6.5 THE NAMING SCREEN” on page 19.
5. On the TAGS screen, use the [ARROW] keys and [YES] key to select the appropriate tags for your
    preset, and then select <SAVE> and press [YES].

### 9.6 THE SETUP MENU

```
Press [FUNC] + [FLTR] to open the SETUP menu. Here you can set a number of track and kit related pa-
rameters. Use the [UP] and [DOWN] arrow keys to move between the options. Press [YES] to confirm your
selection. Press [NO] to exit the menu.
```
#### 9.6.1 KIT

```
Here you can select which tracks that should be affected by control all and set up the track routing to
the compressor.
```
```
CONTROL ALL CONFIG
Use the [TRIG] keys to select which tracks that you want to be affected by the control all functionality.
MIDI tracks can not be selected. For more information, please see “6.2.2 CONTROL ALL” on page
19.
```
- Control all operations also affects the active track, whether it is selected to be affect-
    ed or not.
- The control all functionality is not available for the MIDI tracks.

```
COMPRESSOR ROUTING
Use the [TRIG] keys to select which tracks, inputs and effects that you want to be affected by the
compressor. MIDI tracks can not be selected. Use the [UP]/[DOWN] to navigate between the two
pages.
```

##### 9. PATTERNS, KITS AND PRESETS

##### TRACK LAYERING

```
Track Layering makes it possible to trig two or more tracks with the same note from the sequencer or
the keyboard. For example, if you set track 1 to trigger track 2 then every note you play on track 1 also
plays on track 2, effectively playing two presets at the same time. However, the layering will not cas-
cade trigs. If track 3 is set to be triggered by track 2, and track 2 is set to be triggered by track 1, trigs
placed track 1 will not trigger track 3. Tracks that are layered to the active track have filled squares in
the parameter representation on the screen.
Use [FUNC] + [TRK] to select the active track, then use the [TRIG] keys to select which tracks that
will be layered to the active track.
```
##### PATTERN TRANSPOSE

```
Use the [TRIG] keys to select/deselect which tracks that you want to be affected by pattern transpose.
```
#### 9.6.2 TRACK

Here you can set a number of track related parameters. Use [UP] and [DOWN] to move between the
options. Use [LEFT] and [RIGHT] to select settings or press [YES] to open sub-menus. Press [NO] to
exit the menu.

```
PLAY MODE
Here you can set if a preset is poly- or monophonic and if the LFOs for every voice are synced or not.
P O LY The Sound is polyphonic, and the LFOs for each voice are running independently of each
other.
```

##### 9. PATTERNS, KITS AND PRESETS

```
POLY M.LFO In POLYPHONIC WITH MONO LFO mode, the preset is polyphonic, and the LFOs for
each voice are synced together with the first voice LFO. The LFO of the first voice played deter-
mines the cycle of the LFOs, and the subsequently played voices’ LFOs are synced to this cycle.
This makes the LFO behave like a single or monophonic LFO. This is useful, for example, if you want
to use the LFO for a tremolo effect.
MONO The preset is monophonic, and the envelope is always triggered by each note on/key press.
MONO LEG. In MONO LEGATO mode, the preset is monophonic, but the envelopes are not trig-
gered by a subsequent note on/key press if the first note is still held.
```
```
The number of voices used to play a preset is also affected by the LOCKED VOICES
setting in the VOICE MENU. For more information, please see “10.7 VOICE SETUP
MENU” on page 45.
```
```
MONO NOTE PRIO
Here you can set the note priority, i.e., what note is played if more than one note is played at the same
time. This setting is only available If PLAY MODE is set to MONO or MONO LEG.
LAST gives priority to the last note played.
LOW gives priority to the lowest note played.
HIGH gives priority to the highest note played.
```
```
VELOCITY TO VOL
Selects how MIDI velocity affects volume when playing the preset from a MIDI keyboard.
OFF means velocity does not affect the volume of the sound.
LOG applies a logarithmic velocity curve. The volume difference are greater between softer key-
board presses than between harder.
LIN applies a linear velocity curve. The volume difference between keyboard presses corresponds
linearly to the force applied.
EXP applies an exponential velocity curve. The volume difference are greater between harder
keyboard presses than between softer.
EXP 2 applies an exponential velocity curve. The volume difference are greater between harder
keyboard presses than between softer. The EXP 2 curve has a higher starting point, meaning that a
low velocity has a higher impact on the volume than on EXP.
```
```
OCTAVE
Sets the base octave of the preset. It also makes preset locking more practical since you can control
the octave setting on the preset and minimize the need to transpose the sequencer notes to make
two or more presets work together.
```
```
PITCH BEND DEPTH
Sets how much the pitch bend data from external MIDI devices affects the Digitone II.
```
```
PORTAMENTO
Here you find settings related to the portamento. For more information, please see “11.3 TRIG PAGE 2”
on page 56.
TYPE
TRACK When you play notes, the pitch glides from the pitch of the last note played to the next
new note played on the track.
VOICE When you play notes, the pitch glides from the pitch of the last note played by a specific
voice to the pitch of the next new note played by the same voice.
LEGATO ONLY When playing notes, the pitch glides from the pitch of the last note played and
held (the key pressed down and not yet released) to the pitch of the next new note played on
the track.
SLOPE
CONSTANT RATE The pitch glides linearly at a constant rate. Longer glides take longer time to
complete.
CONSTANT TIME The pitch glides linearly, but the glide is completed in a specific time, regard-
less of the interval between the start and end notes. Glides over wider note intervals, therefore,
have a higher rate.
```

##### 9. PATTERNS, KITS AND PRESETS

##### AMOUNT

```
This setting enables partial glides where just the final part of the interval is included in the glide. For
example, a setting of 100 gives a full glide from start to finish. Lower values start the glide closer to
the goal pitch.
STYLE
GLIDE Ordinary continuous portamento.
GLISSANDO Quantizes the portamento to semitones.
GATING
OFF The glide continues after the key is released.
ON The glide stops when the key is released.
```
KEY TRACKING

Opens a menu where you can assign up to four parameters to the key tracking. Both the NOTE
parameter on the TRIG menu and the key track information from incoming MIDI sent to the Digitone II
from external devices affects the assigned parameters. Press [YES] to open the menu.

You can select parameters from the SYN, FLTR, AMP, FX, and MOD PARAMETER pages. Turn the
DATA ENTRY knobs A–D to select the track parameters that you wish to assign. Press [YES] to con-
firm the selection. Then use DATA ENTRY knobs E–H to set the modulation depth of the four track
parameters. The depth is an offset of the original track parameter value.

Turn LEVEL/DATA to test your settings.

```
For key tracking, all modulation are centered around middle C (C5). A positive modula-
tion value increases the parameter value when playing above C5 and decreases when
you play a key below C5. A negative modulation value inverts this behavior.
For example: With a modulation value set to 8, each consecutive note changes the pa-
rameter value by 1. With a modulation value set to 127, each consecutive note changes
the parameter value by 16, meaning that you reach the full parameter rage in 8 notes
```
VELOCITY MOD

Opens a menu where you can assign up to four parameters to the velocity parameter. Both the VEL
parameter on the TRIG menu and the velocity of incoming MIDI notes sent to the Digitone II from ex-
ternal devices affects the assigned parameters. Press [YES] to open the menu. Selecting parameters
and setting modulation depth works in the same way as for KEY TRACKING.

AFTERTOUCH

Opens a menu where you can assign up to four parameters to the MIDI aftertouch command. Press
[YES] to open the menu. Selecting parameters and setting modulation depth works in the same way
as for KEY TRACKING.

PITCH BEND

Opens a menu where you can assign up to four parameters to the MIDI pitch bend command. Press
[YES] to open the menu. Selecting parameters and setting modulation depth works in the same way
as for KEY TRACKING.

MODULATION WHEEL

Opens a menu where you can assign up to four parameters to the MIDI mod wheel command (CC #1).
Press [YES] to open the menu. Selecting parameters and setting modulation depth works in the same
way as for KEY TRACKING.


##### 9. PATTERNS, KITS AND PRESETS

##### BREATH CTRL

```
Opens a menu where you can assign up to four parameters to the MIDI breath controller command
(CC #2). Press [YES] to open the menu. Selecting parameters and setting modulation depth works in
the same way as for KEY TRACKING.
```
```
RENAME
Opens the NAMING menu and let you rename the preset of the active tracks.
```
### 9.7 ARPEGGIATOR MENU

```
The ARPEGGIATOR menu controls the preset's arpeggiator. These settings are part of the preset and
saved together with the preset. The arpeggiator is not available for the MIDI tracks. Press [ARPEGGIATOR]
to open the ARPEGGIATOR menu. Press [TRIG 1–16] to select the presets whose arpeggiator are edited.
Press [FUNC] + [ARPEGGIATOR] to toggle the ARPEGGIATOR on and off.
```
#### 9.7.1 MODE

```
Activates the arpeggiator and controls how the arpeggiated notes are sorted.
OFF deactivates the arpeggiator.
TRUE plays the notes in the same order as they are inserted.
UP plays the notes in ascending order, from the lowest note to the highest, on a per octave basis.
DOWN plays the notes in descending order, from the highest note to the lowest, on a per octave basis.
CYCL plays the notes first in ascending order, then in descending order.
```
#### 9.7.2 SPEED

```
Speed sets the speed of the arpeggiator. It is synchronized to the BPM of the project. (1/1–1/96).
```
#### 9.7.3 RANGE

```
Range sets the octave range of the arpeggiator. After each completed arpeggiator cycle, the arpeggiat-
ed notes are transposed one octave up. When the notes have reached the octave offset specified by the
RANGE setting the notes are reset to their initial values. From there the octave transpose is started all
over again.
```
#### 9.7.4 N.LEN

```
Note Length controls the length of the arpeggiated notes.
```
#### 9.7.5 LEN

```
Turn DATA ENTRY knob F to select the length of the arpeggio. The max length is 16 sequencer steps.
```
#### 9.7.6 OFS

```
Offset selects the offset (note) value of the chosen arpeggiator step in semi-tones. The offset is from the
original note trig.
```
1. Press [LEFT] or [RIGHT] to select which arpeggiator step to offset.
2. Use DATA ENTRY knob E to set the offset.
3. Press [DOWN] to mute an arpeggiator step. Press [UP] to activate an arpeggiator step that has
    been deactivated.

```
Copy, Paste, Clear commands are available in the arpeggiator menu. For more information,
please see “6.4 COPY, CLEAR, AND PASTE” on page 19.
```

## 10. THE SEQUENCER

10. THE SEQUENCER

The sequencer of the Digitone II stores information in patterns. A pattern controls the playback of the
tracks and various pattern-specific aspects of the tracks. Patterns are stored in projects. A project contains
8 banks (A to H) with 16 patterns each, which means 128 patterns are available for each project. For more
information, please see “13.3 PATTERN MENU” on page 71.

A pattern contains:

- General trig settings on the TRIG page (default note pitch, velocity et cetera).
- The kit with all parameter settings on the SYN, FLTR, AMP, FX and MOD pages.
- The settings on the DELAY, REVERB, CHORUS and MIXER pages.
- The settings in the SETUP menu.
- Quantization settings.
- Note trigs and Lock trigs for all tracks.
- Parameter/preset locks.
- Length and time signature for the tracks.

### 10.1 PATTERN OPERATIONS

Digitone II can seamlessly switch between patterns. This, together with the ability to chain patterns, is a
handy feature when improvising live. For more information, please see “10.1.3 CHAINS” on page 39.

#### 10.1.1 SELECTING BANK AND PATTERN

```
Press [PTN] and then use the [LEFT]/[RIGHT] keys to select a bank (if you want to select a pattern
from another bank than the current). Then press [TRIG 1–16] to select a pattern.
There is a secondary way to change bank and pattern, more similar to earlier Elektron devices. Press
[FUNC] + [PTN], and then press [TRIG 9–16] to select a bank. Then press [TRIG 1–16] to select a pat-
tern within that bank.
If you just want to select a pattern within the current bank, press [PTN] + [TRIG 1–16].
Press [PTN] or [NO] to exit bank/pattern selection.
White [TRIG] keys indicate pattern positions that contain data. A red [TRIG] key indicates the current
active pattern. Empty patterns are unlit.
When a pattern is playing, and a new pattern is selected, the new pattern number will be shown flashing
in the upper left corner of the screen. Once the last step of the pattern has played, the new pattern will
start, and the pattern number will cease to flash.
```
- Patterns can be changed while the sequencer is running.
- Patterns can be changed and queued by sending program change messages.
- It is possible to copy a pattern, and then paste it to one or several locations without
    leaving the active pattern. You can also clear one or several non-active patterns.
    - To copy, first press [PATTERN]. Then press [TRIG] + [RECORD] to select the pattern
    you wish to copy and copy it. Then press [TRIG] + [STOP] to select to where you want
    to paste the pattern and paste it.
    - To clear, first press [PATTERN]. Then press [TRIG] + [PLAY] to select the pattern you
    wish to clear and clear it.

#### 10.1.2 PATTERN CONTROL

```
Press [PLAY] to start the playback of a pattern. Press [STOP] to stop the playback of all tracks. The
sound will be cut off, but effects like Delay will continue to be audible until the delay repeats have faded
out. Quickly press [STOP] + [STOP] to stop playback of all tracks and get just a short fade out of the
send effects.
When a pattern is playing, press [PLAY] to pause the playback. Press [PLAY] to resume the playback.
If a pattern contains more than 16 sequencer steps, the <PATTERN PAGE> LEDs will indicate this. When
a pattern is playing, the currently active pattern page is shown with a flashing, <PATTERN PAGE> LED.
```
#### 10.1.3 CHAINS

```
Chains are sequences that consist of more than one pattern. You can use chains to preselect and auto-
mate the order in which you want your patterns to play
```

##### 10. THE SEQUENCER

```
You can create one chain and it can contain any pattern in bank A–H. The chain can consist of up to 64
patterns.
```
1. Press [PTN], and then press [FUNC] + [YES] to open the chain creator screen.
2. Use the [LEFT]/[RIGHT] keys to select a bank (if you want to select a pattern from another bank
    than the current). Then press [TRIG 1–16] to select a pattern. Repeat until you have created the
    desired chain.
3. (Optional) Press [FUNC] + [LEFT] to delete the last pattern from the chain.
4. Press [YES] to confirm the pattern chain and close the chain creator screen. Press [NO] if you
    wish to cancel the pattern chain and close the chain creator screen.
5. Press [PLAY] to start the sequencer and play the chain. The chain will be looped once the final
    pattern of the chain has played.
To exit chain mode, select a pattern or song using the normal selection process.
There is also a legacy procedure for creating chains.
1. Press and hold [PTN] and then press a [TRIG 1–16] key to select the first pattern in the chain.
2. Release the [PTN] key and then press [TRIG 1–16] keys in the same order as you want the chained
patterns to play. Keep a previous [TRIG] key pressed while you press the next one, the one after
that and so on. You can press the same [TRIG] key again if you wish to add the pattern multiple
times in a row. If you want to add a pattern from another bank, use the [LEFT]/[RIGHT] keys to
select a bank and then press [TRIG 1–16] to select pattern.
3. Press [PLAY] to start the sequencer and play the chain. The chain will be looped once the final
pattern of the chain has played.

```
Please note that the chain will be lost when you create a new chain or when you select a new
pattern/song. Chains cannot be saved and will be lost when you switch the Digitone II off.
```
- Chains can be created while the sequencer is running.
- You can also use MIDI program change messages from an external device to change
    patterns on the Digitone II. For more information, please see “13.4.1 SYNC” on page 72.

### 10.2 EDITING A PATTERN

```
Digitone II offers several modes of input when creating or editing a pattern. Three of them are, GRID RE-
CORDING mode, LIVE RECORDING mode, and STEP RECORDING mode. In these modes, two types of
trigs can be entered: Note trigs and Lock trigs.
```
#### 10.2.1 TRIG TYPES

```
A trig is a sequencer event that you can place when you want the sequencer to perform an action on the
Digitone II. There are two types of trigs that you can use, note trigs and lock trigs.
```
- NOTE TRIGS trigger preset notes or MIDI notes.
- LOCK TRIGS trigger parameter locks (but do not trigger notes). For more information, please see
    “10.12.1 PARAMETER LOCKS” on page 48.
Note trigs are indicated by red [TRIG] keys and lock trigs are indicated by yellow [TRIG] keys. Unlit
[TRIG] keys indicate steps that does not contain any trigs. Read more about parameter locks in section
“10.12.1 PARAMETER LOCKS” on page 48. Trigs are added differently in the sequencer, depending on
whether GRID RECORDING, LIVE RECORDING, or STEP RECORDING mode is active. Note trigs can
also be added in NOTE EDIT mode.


##### 10. THE SEQUENCER

### 10.3 GRID RECORDING MODE

GRID RECORDING is a method of composing where you use the [TRIG] keys to add trigs.

1. Press [RECORD] to enter GRID RECORDING mode. The [RECORD] key lights up red to indicate that
    GRID RECORDING mode is active.
2. Select the track to which you want to add trigs by pressing and holding [TRACK] and then one of the
    [TRIG] keys. A red [TRIG] key indicates the active track.
3. Place note trigs on the sequencer using the sixteen [TRIG] keys. To add a lock trig, press [FUNC] +
    [TRIG]. Quickly pressing the [TRIG] key of any of the trigs entered will remove the trig. Pressing a
    [TRIG] key and holding it slightly longer will prepare the trig for editing, rather than removing it.
4. Select another track, and add note trigs. Repeat the procedure for all the tracks you want to use.
5. Press [PLAY] to listen to the sequence.

You can transpose a note trig by pressing and holding the [TRIG] key, and then press [+]/[-].

Add micro timing to a note trig by pressing and holding the [TRIG] key, and then press [LEFT]/[RIGHT].
For more information, please see “10.8 MICRO TIMING” on page 46.

Set the retrig speed by pressing and holding the [TRIG] key while navigating to TRIG PAGE 2 keys and
make the desired settings. For more information, please see “10.9 RETRIGS” on page 46, and “11.3 TRIG
PAGE 2” on page 56.

If the pattern contains more than 16 steps, select the pattern page you want to edit by pressing the [PAGE]
key. A fully lit <PATTERN PAGE> LED shows the active pattern page.

Press [RECORD] to exit the GRID RECORDING mode.

- If your pattern has more than one page you can easily select which page you want to
    edit. Press and hold [PAGE], and then press one of the [TRIG 1–8] keys that light up to
    select the active page for editing. You can also cycle through the pages with the [LEFT]
    and [RIGHT] keys while holding [PAGE]. For more information, please see “10.11 PAGE
    SETUP MENU” on page 46.
- You also have the possibility to select which pages of your pattern you want the se-
    quencer to play and loop. The selected pages are then looped on playback.
    In GRID RECORDING mode, press and hold [PAGE], and then press [TRIG 9–16] to
    select which pattern pages you want to play and loop. Green [TRIG] keys shows the
    selected pages. The [TRIG 9–16] keys corresponds to pattern page 1–8. For example,
    you have an eight page pattern and you want to play and loop page 1, 2,5, and 7. To ac-
    complish this, press and hold [PAGE] and then press [TRIG 9, 10, 13, 15]. On the top row,
    [TRIG 1–8], the blinking [TRIG] key shows the master playback position. On the bottom
    row, [TRIG 9–16], the blinking [TRIG] key shows the page currently playing. Press and
    hold [PAGE] and then press [NO] to resume normal playback of all pages in the pattern.
- You can use an external MIDI controller such as a keyboard to input NOTE and TRIG
    VELOCITY data when you are in GRID RECORDING mode. Just press and hold a [TRIG]
    key, and then play a note on the external keyboard. The first note sets the TRIG VELOC-
    ITY value for all notes on the trig.
- Press [TRIG] + [YES] to preview a specific trig. The preview includes any parameter
    locks you have placed on that trig.
- Press [TRIG] + [PRESET/KIT] to save the sound of a trig as a preset, taking all parame-
    ter locks into consideration.
- If you use an external MIDI controller to record to the Digitone II MIDI tracks, the se-
    quencer receives data on the Auto MIDI channel and records on the active track. For
    more information, please see “13.4.3 CHANNELS” on page 74.
- All trigs of a track can be shifted forwards or backward on the sequencer. While in GRID
    RECORDING mode, hold down [FUNC] while pressing the [LEFT] or [RIGHT] arrow
    keys to perform the trig shift.

#### 10.3.1 NOTE EDIT MENU

```
The NOTE EDIT menu is part of the GRID RECORDING mode and gives you the possibility to separately
edit each note of a step, even if they are part of a polyphonic chord. You can edit four different param-
eters. NOTE, LEN, and VEL are the same parameters that can be found on the TRIG PAGE 1. For more
information, please see “11.2 TRIG PAGE 1” on page 55. The fourth parameter, TIME lets you adjust
the timing of each note. You can also use the NOTE EDIT menu to add new notes.
```

##### 10. THE SEQUENCER

##### EDITING NOTES ON ALREADY PLACED TRIGS

```
In GRID RECORDING mode, press the placed [TRIG] + [NOTE EDIT] to open the NOTE EDIT menu. Use
[UP]/[DOWN] to highlight the note you want to edit. Then use DATA ENTRY knobs E–H to change the
parameters. Hold [FUNC] and then turn DATA ENTRY knobs E–H to change the parameters to affect all
notes on that step.
NOTE Sets the note value for the selected note. The selectable notes are limited to the ones existing
in the scale selected in the KEYBOARD SETUP menu if NOTE PARAM is set to SCALE in the PER-
SONALIZE menu. For more information, please see “8.5.2 KEYBOARD SETUP MENU” on page 24.
Press and turn DATA ENTRY knob A to select any note in the Chromatic scale.
TIME Sets the micro timing for the selected note. A positive value moves the note later. A negative
value moves the note earlier. For example, a value of 12 moves the note halfway towards the next trig.
A value of -6 moves the note a quarter of the way towards the previous trig. The full range is -23–23.
LEN Sets the note length for the selected note.
VEL Sets the velocity for the selected note.
```
```
Use the following shortcuts for further editing.
```
- [FUNC] + [LEFT]/[RIGHT] to go to the previous/next sequencer step that contains a note trig.
- [LEFT]/[RIGHT] to go the previous/next sequencer step regardless if it contains a trig or not.
- [FUNC] + [UP] to delete the highlighted note.
- [FUNC] + [DOWN] to add a note below the highlighted note.
- [FUNC] + [YES] to preview the selected step. The preview only takes the NOTE and VEL settings
    into account. The TIME and LEN settings can not be previewed.

```
ADDING AND EDITING NOTES USING NOTE EDIT
Press [NOTE EDIT]. GRID RECODING mode is switched on and by default, The [TRIG] keys are set to
work like in KEYBOARD mode. You can also select which step to edit manually by pressing [FUNC] +
[TRIG] key. Press [FUNC] + [PAGE] to navigate to the next pattern page.
```
```
You can also use an external MIDI keyboard to add notes in the NOTE EDIT menu.
```
1. Connect an external MIDI controller to the Digitone II.
2. Set the controllers MIDI channel to the same channel as Digitone II's AUTO CHANNEL
3. Press [NOTE], and then select the active step that you wish to record on..
4. Press [REC], and then press the keys on the MIDI controller
The note values, the length, and the velocity are then recorded om the active step.

### 10.4 LIVE RECORDING MODE

```
LIVE RECORDING mode is the second method of adding trigs to the tracks. In this recording mode, the
[TRIG] keys are played in real time to input trigs to the tracks. You can also use KEYBOARD mode to add
note trigs chromatically. Additionally you can add velocity, retrigs, and change presets using the TRIG
modes. It is also possible to enter parameter locks in real time. Trigs input in LIVE RECORDING mode can
be automatically quantized or not. Non-quantized trigs can be quantized after they are recorded, by using
the QUANTIZE menu that is accessed by pressing [FUNC] + [TRIG]. For more information, please see
“10.10 QUANTIZE MENU” on page 46.
```
1. Press and hold [RECORD], then press [PLAY] to enter LIVE RECORDING mode. (Press [PLAY] twice
    while keeping the [RECORD] key pressed to activate/deactivate automatic quantization.) The se-
    quencer starts to play, and the [RECORD] key starts to flash red.


##### 10. THE SEQUENCER

2. Enter trigs in real time by pressing the [TRIG] keys. If KEYBOARD mode is active, the pitch value
    of the note trig will be recorded according to which [TRIG] key is pressed for the active track. Any
    changes to PARAMETER page settings, using the DATA ENTRY knobs, will be recorded as parameter
    locks and add lock trigs where needed.
3. Press [PLAY] to exit LIVE RECORDING mode while keeping the sequencer playing. If LIVE RECORD-
    ING mode is active and [RECORD] is pressed, GRID RECORDING mode will be activated.
4. Press [STOP] to stop both recording and playback of the sequencer.

```
You can use an external MIDI controller such as a keyboard to input NOTE, TRIG
VELOCITY, and TRIG LENGTH data in LIVE RECORDING mode. Just play the notes on
the external keyboard and they will be recorded by the sequencer. The first note sets the
TRIG VELOCITY value for all notes on the trig. The last note that is released sets the TRIG
LENGTH for all notes on the trig.
```
### 10.5 STEP RECORDING MODE

STEP RECORDING is a quick and straightforward method of placing trigs on the sequencer. In this record-
ing mode, you insert notes by pressing the [TRIG] keys or by using an external MIDI controller. The se-
quencer then captures the note and automatically advances to the next step. There are two different modes
available, STANDARD mode and JUMP mode.

```
STANDARD mode
```
1. Press [RECORD] + [STOP] to enter STEP RECORDING mode. The [RECORD] key starts to
    double-blink red. (Press [STOP] twice while keeping the [RECORD] key pressed to toggle between
    STANDARD and JUMP mode).
2. Press a [TRIG] key to select the active step to where you want to start to add note trigs. The active
    step is shown with a green [TRIG] key that double-blinks. (If positioned on an earlier placed trig, it
    inherits the light pattern of that trig). You can also use [LEFT]/[RIGHT] to select the active step or
    skip steps.
3. Press and hold [FUNC] and then press [TRIG 1–16] key to add a note trig on the corresponding
    track to the active step. The active step then automatically advances to the next step.
    You also have the option to add note trigs chromatically on the selected active track.
4. Press [TRK] + [TRIG 1–16] to select the track to which you want to add trigs. A red [TRIG] key
    indicates the active track.
5. Press and hold [KEYBOARD] and then press any lit [TRIG] key to add a note trig with the corre-
    sponding note value to the selected step. The active step then automatically advances to the next
    step. You can use [+]/[-] or [UP]/[DOWN] to transpose the chromatic keyboard on the [TRIG] keys
    up or down in octaves.
6. To remove a trig or to add a rest (no trig), position the active step on the trig that you want to
    remove or add a rest to, and then press [NO]. The active step then automatically advances to the
    next step.
7. To add a parameter lock to a trig, press and hold a [TRIG] key, and then turn the DATA ENTRY
    knob that controls the parameter you want to lock and set it to the desired value. The graphics
    become inverted for the locked parameter, and the locked parameter value is displayed. The [TRIG]
    key of the locked trig blinks red (for note trigs) or yellow (for lock trigs) to indicate that the trig now
    contains a parameter lock.
8. Press [RECORD] to exit STEP RECORDING mode.

```
JUMP mode
In JUMP mode, the LEN parameter setting on the TRIG PARAMETER page controls the note length of
the trigs you place, and advances the active step the same length. A LEN value of 1/16 adds a sixteenth
note and advances the sequencer one step. 1/8 adds an eighth note and advances the sequencer two
steps. 1/4 adds a quarter note and advances the sequencer four steps and so on. The LEN parameter is
also parameter locked on every trig you place.
```

##### 10. THE SEQUENCER

- Press [PLAY] to listen to the sequence while you are programming it. Press [STOP] to
    stop the sequencer and stay in STEP RECORDING mode.
- If you use an external MIDI controller, you must set it to send MIDI data on the Digitone
    II’s defined AUTO CHANNEL. For more information, please see “13.4.3 CHANNELS” on
    page 74.
- If you use an external MIDI controller, the trig’s VEL (velocity) value is determined by the
    velocity sent from these and is parameter locked.
- If you press and hold [FUNC] while inputting notes from an external MIDI controller, the
    velocity is fixed and determined by the VEL parameter setting.
- If you press and hold [YES] while you place a trig with [TRIG] keys or an external MIDI
    controller, the trig length is locked to the time you press the [TRIG] keys or the external
    MIDI controller.
- If you add a new note trig on a previously placed note trig, any parameter locks previ-
    ously placed on that trig remain as they were.
- In STEP RECORDING mode, to avoid accidental key presses, [FUNC] + [NO] does not
    reload the pattern. [FUNC] + [YES] does not save the pattern. Instead they work as
    described above.

### 10.6 SEQUENCER MENU (EUCLIDEAN MODE)

```
The Euclidean sequencer mode uses two separate pulse generators to generate events (trigs) that are
placed as evenly spread out over the sequencer track’s trigs as possible. You can also set a Boolean logic
operator to combine or subtract the trigs from the two pulse generators in different ways.
When you turn on Euclidean mode for a track, the [REC] button will turn blue indicating the mode is active,
and the [TRIG] keys where the generated trigs are placed will also be blue. Any trigs placed on the se-
quencer before activating the Euclidean mode will be hidden and inactive while in this mode, but they’ll
reappear when you turn the mode off again. If you want to convert an Euclidean sequence into regular
sequencer trigs, just press and hold [FUNC] while turning the Euclidean mode off. This will remove any trigs
that were placed prior to entering Euclidean mode.. While in Euclidean mode, it is not possible to add note
trigs manually to the sequencer. However, you can still do parameter locking on the already generated trigs.
Press [FUNC] + [AMP] open the SEQUENCER menu.
```
```
PL1 Pulse generator 1 sets the number of pulses (trigs) placed on the sequencer by this generator.
PL2 Pulse generator 2 sets the number of pulses (trigs) placed on the sequencer by this generator.
LEN Track length sets how many steps the track will have. This parameter is only available in PER
TRACK mode. For more information, please see “10.11 PAGE SETUP MENU” on page 46.
EUC Euclidean mode switches the Euclidean sequencer mode on/off.
R01 Rotation generator 1 rotates the trigs placed by generator 1 forward or backwards on the track.
R02 Rotation generator 2 rotates the trigs placed by generator 2 forward or backwards on the track.
TRO Track rotation rotates the trigs placed by both generator 1 and generator 2 forward or backwards
on the track.
OP Boolean operator lets you add or subtract the trigs generated from the two pulse generators in dif-
ferent ways based on mathematical logical expressions.
OR All trigs from both generator 1 or 2 are placed on the track.
XOR Trigs from both generator 1 or 2 are placed on the track unless they are added to the same
sequencer step.
```

##### 10. THE SEQUENCER

```
AND Only trigs added to the same sequencer step by generator 1 and 2 are placed on the track.
SUB Trigs from generator 1 are placed on the track unless generator 2 adds a trig on the same step
as generator 1.
```
```
In Euclidean mode, please note that if you press any already generated trigs, they will remain
in regular sequencer mode when you switch Euclidean mode off.
```
### 10.7 VOICE SETUP MENU

Press [VOICE SETUP] to open the VOICE SETUP menu, where you handle the allocation of the Digitone II’s
16 voices. In this menu, you can also make the settings for unison and voice stealing. Press [TRK] + [TRIG
1–16] to select which track to edit. At the top of the screen, you can see what track is selected. The center
of the screen shows the currently playing voices. Use the DATA ENTRY knobs to change the settings. Press
[NO] to exit the menu. The voice settings are stored in the active pattern.

```
VOICE STEAL The Digitone II has 16 voice polyphony. VOICE STEAL sets the rules for how voices are
stolen (which voice is taken when you play a new note) when you use more than 16 voices at the same
time. The VOICE STEAL parameter affects the whole pattern.
CYCLE The first played note is stolen first.
TRACK Track priority. Notes played on track 1 has priority over notes played on track 2–16. Track 2
notes over track 3–16. Track 3 notes over track 4–16 etc.
LO Lowest note is stolen first
HI Highest note is stolen first.
REUSE Sets if the same note played twice should reuse the same voice, or cycle to use another free
voice.
ON Reuses the same voice. Useful for staccato effects and drums.
OFF Cycles to use another voice. Good for piano-like sounds and pads.
VOICES Sets the number of voices (1–16) you want to lock to a specific track. A locked voice cannot be
used or stolen by another track. If you lock voices to a track, the track cannot use more voices than the
ones that are locked to it. The first number shows the number of locked voices to the track. The number
in brackets shows how many unlocked voices that remain. If you set the parameter to DYN (dynamic) it
will use any available voices to play its preset.
```
```
The number of voices that are used to play a preset is also affected by the PLAY MODE
setting in the SETUP menu. For more information, please see “9.6 THE SETUP MENU” on
page 34.
```
```
STAT E Unison State turn unison on/off. Unison gives the possibility to add several voices to play the
same note.
V.AMNT Unison Voice Amount sets the number of voices that plays for each note.
```
```
[FUNC] + [VOICE SETUP] toggles unison on/off.
```
```
SPREAD Unison Spread adds pan and detune to the unison voices.
```
```
You can copy the settings in the VOICE menu and paste it to another pattern. In the VOICE
menu, press [FUNC] + [RECORD] to copy the parameter settings. Change pattern and open
VOICE menu, and then press [FUNC] + [STOP] to paste the settings.
```

##### 10. THE SEQUENCER

### 10.8 MICRO TIMING

```
Here you can add micro timing to a note trig, moving it ahead or behind the beat. Micro timing can be
customized on any of the sequencer steps on both audio and MIDI tracks. In GRID RECORDING mode,
press and hold one or several [TRIG] keys and then press [LEFT]/[RIGHT] to access the pop-up Micro
timing menu that shows the time offset for the chosen sequencer step(s) on the active track. Press [LEFT]/
[RIGHT] to adjust the time offset. To exit the MICRO TIMING menu, release the [TRIG] key(s). The micro
timing settings are stored in the active pattern.
```
### 10.9 RETRIGS

```
Retrigs can be customized on any of the sequencer steps on the audio tracks (the retrig function is not
available on the MIDI tracks). In GRID RECORDING mode, press [TRIG PARAMETERS] twice to access the
RETRIG PARAMETERS page. Hold one or several [TRIG] keys and then set the desired retrig options for
the trig(s).
For more information about the parameters on this page, please see “11.3 TRIG PAGE 2” on page 56.
```
### 10.10 QUANTIZE MENU

```
The quantization affects all micro timed and off grid trigs on the sequencers tracks. Press [FUNC] +
[TRIG PARAMETERS] to access this menu. Change settings with DATA ENTRY knob E and H.
```
```
TRACK affects all the trigs of the active track in real time. The higher the quantize value, the more the
trigs will be quantized. Press [TRIG 1–16] key to select track to quantize.
PATTERN affects all the trigs of all tracks in the pattern in real time. The higher the quantize value, the
more the trigs will be quantized.
Press [NO] to exit the menu.
```
### 10.11 PAGE SETUP MENU

```
You can set the length and timing of the pattern and the tracks in this menu. The sequencer has up to 128
steps spread over 8 pages with 16 steps each. The PAGE SETUP menu has two modes. In PER PATTERN
mode all tracks share the same length. In PER TRACK mode, each track can have different lengths. Press
[FUNC] + [PAGE] to access the PAGE SETUP menu. Press [FUNC] + [YES] to toggle between the two
modes. Use the DATA ENTRY knobs to adjust the settings.
```
#### 10.11.1 PER PATTERN MODE

```
In this mode, all tracks of the pattern share the same length and time signature.
```

##### 10. THE SEQUENCER

```
LENGTH sets the amount of steps on the current pattern page. The leftmost number displays the
number of steps selected. If you use 17 steps or more in a pattern, the [PAGE] key is (while in GRID
RECORDING mode) used to toggle between the different pattern pages.
SPEED controls the speed of pattern playback in multiples of the current tempo. It offers seven pos-
sible settings, 1/8X, 1/4X, 1/2X, 3/4X, 1X, 3/2X and 2X. A setting of 1/8X plays back the pattern at one-
eighth of the set tempo. 3/4X plays the pattern back at three-quarters of the tempo; 3/2X plays back
the pattern twice as fast as the 3/4X setting. 2X makes the pattern play at twice the BPM.
```
When you extend the length of a pattern, its trigs copies automatically over to the new steps. (If this
option is selected in SETTINGS > PERSONALIZE > AUTO COPY) If a pattern consists of for example
two pages and the pattern length is increased to four pages, the two additional pattern pages are then
copies of the first two pattern pages.

Press the [PAGE] key repeatedly to change the total length of the pattern quickly. Press the [TRIG] keys
to change the number of steps of the pattern quickly.

```
A 2X speed setting is useful for increasing the base resolution of the step sequencer to
32nd notes. A 3/4X setting is useful when Digitone II is playing alongside other instru-
ments set to the same BPM, and you want Digitone II to play triplets.
```
#### 10.11.2 PER TRACK MODE

In this mode, you can assign individual length and speed to the tracks of the pattern. Press [FUNC] + [YES]
to switch between the two modes. In PER TRACK mode, you find two sections, TRACK and PATTERN.

The TRACK column sets the step length and speed of the track. The settings only affects the active track.

The PATTERN column sets the master change length and the master length of the pattern.

```
LENGTH sets the amount of steps on the current pattern page. The leftmost number displays the
number of steps on the active pattern page. If you use 17 steps or more in a pattern, the [PAGE] key is
(while in GRID RECORDING mode) used to toggle between the different pattern pages.
SPEED controls the speed of track playback in multiples of the current tempo. It offers seven possible
settings, 1/8X, 1/4X, 1/2X, 3/4X, 1X, 3/2X and 2X. A setting of 1/8X plays back the track at one-eighth
of the set tempo. 3/4X plays the track back at three-quarters of the tempo; 3/2X plays back the track
twice as fast as the 3/4X setting. 2X makes the track play at twice the BPM.
CHANGE (Pattern Change) controls for how long the active pattern plays before it changes to a cued
or chained pattern. This setting is important when, for example, you set RESET to INF. Then, if you do
not make any CHANGE setting, the pattern plays infinitely and the next cued pattern will never play. By
default a cued pattern change happens at the end of the pattern (CHANGE set to OFF).
RESET (Pattern Reset) controls the number of steps the pattern plays before all tracks resets and re-
starts from the first step on the first page. An INF setting makes the tracks of the pattern loop infinite-
ly, without ever being restarted. Note that this setting also affects for how long the active pattern plays
before a chained pattern starts playing. The CHANGE parameter overrides this if its parameter value
is less than the RESET value.
```

##### 10. THE SEQUENCER

```
In the PAGE SETUP menu, you can press [FUNC] + [UP]/[DOWN] to set the TRACK
LENGTH in increments from 2/16 to 128/128.
```
### 10.12 SEQUENCER FEATURES

#### 10.12.1 PARAMETER LOCKS.

```
Parameter locks makes it possible to set every trig to have its own unique parameter values. The note
trigs of an audio track can, for example, have different pitch, amp or filter settings. It is possible to pa-
rameter lock the parameters found on the PARAMETER pages, and you can apply parameter locks to
all types of tracks. For a complete overview of all parameters on the PARAMETER pages, please see “11.
TRACK PARAMETERS” on page 55, and “APPENDIX A: MACHINES” on page 87.
Adding parameter locks in GRID RECORDING mode.
```
1. Press [RECORD] to enter GRID RECORDING mode.
2. Press a [TRIG] key to add a Note trig or [FUNC] + [TRIG] key to add a Lock trig where you want to
    perform a parameter lock.
3. Press and hold the [TRIG] key of a previously placed trig (note trig or a lock trig).
4. Turn the DATA ENTRY knobs that control the parameter you want to lock, and set it to the desired
    value. The graphics become inverted for the locked parameter, and the locked parameter value is
    displayed. The [TRIG] key of the locked trig blinks red (for note trigs) or yellow (for lock trigs) to
    indicate the trig now contains a parameter lock.
Remove a single parameter lock by holding [TRIG] + pressing the DATA ENTRY knob of the locked pa-
rameter. You can erase all parameter locks from a trig if you remove the note trig and then enter it again.
Adding parameter locks in LIVE RECORDING mode.
1. Press and hold [RECORD], then press [PLAY] to enter LIVE RECORDING mode.
2. Turn a DATA ENTRY knob, or play the [TRIG] keys in KEYBOARD mode, to add parameter locks to
the active track. Note trigs are placed, and parameter locked accordingly, and it also places lock
trigs that contains the parameter locks on the sequencer steps that do not already have any trigs.
Press [NO] + one (or several) of the [TRIG] keys to erase a sequence of recorded trigs on a specific
track (or on several tracks) in time with the sequencer, i.e. all held trigs reached by the pattern’s play-
head will be erased.
You can also remove specific parameter locks on a track in real time. In LIVE RECORDING mode, press
and hold [NO] and then press and hold the DATA ENTRY knob corresponding to the parameter that you
want to remove.
Adding parameter locks in STEP RECORDING mode.
1. Press and hold [RECORD], then press [STOP] to enter STEP RECORDING mode.
2. Press and hold a [TRIG] key, and then turn the DATA ENTRY knobs that control the parameter you
want to lock, and set it to the desired value. The graphics become inverted for the locked parame-
ter, and the locked parameter value is displayed. The [TRIG] key of a locked trig blinks red (for note
trigs) or yellow (for lock trigs) to indicate that the trig contains a parameter lock.
- Up to 80 different parameters can be locked in a pattern. A parameter counts as one (1)
locked parameter no matter how many trigs that lock it. If for example the cutoff param-
eter of the filter is locked on every sequencer step, there are still 79 other parameters
that can be locked.
- In GRID RECORDING mode, if you press and hold a trig that contains parameter locks,
the [PARAMETER] page keys lights up in red to show the location of the locked param-
eters.

#### 10.12.2 PRESET LOCKS

```
You can change a track's preset to another preset from the pool on any individual sequencer step. These
preset locks are an immensely useful feature for adding variations to a track. Press and hold a note trig
and turn the LEVEL/DATA knob to open the pool list. Use the LEVEL/DATA knob to scroll through the
list. Select the preset you want to assign to the note trig and then release the [TRIG] key. Press and hold
the [TRIG] key of the note trig to show the assigned preset. For more information, please see “9.1.1 ADD-
ING PRESETS TO THE POOL” on page 28, and “9.2.1 LOAD (PRESET)” on page 29.
```

##### 10. THE SEQUENCER

#### 10.12.3 TRIG CONDITIONS AND CONDITIONAL LOCKS

Trig conditions are a set of conditional rules that you can apply to any trig, using specific parameter locks
called conditional locks. Each rule is a logical condition that determines whether a trig set on a track in
the sequencer is triggered or not. If the condition is true, then the trig will play. If the condition is false,
the trig will not play.

```
ADDING A CONDITIONAL LOCK
```
1. In GRID RECORDING or STEP RECORDING mode, place a note trig or lock trig on the sequencer
    step to where you want to apply a conditional lock.
2. Press and hold the trig to access the COND (Trig Condition) parameter on the TRIG PAGE 1. For
    more information, please see “11.2 TRIG PAGE 1” on page 55.
3. Turn DATA ENTRY knob H to select one of the following trig conditions:
PRE is true and the trig plays if the most recently evaluated trig condition on the same track was true.
(PRE and PRE conditions are not evaluated and are ignored).
PRE (Not PRE) is true when PRE is false. A trig with this trig condition plays if the most recently
evaluated trig condition on the same track was not true. (PRE and PRE conditions are ignored and not
evaluated.)
Example 1: Trig 1, 50% = True > Trig 2, PRE = False > Trig 3, PRE = False > Trig 4, PRE = True
Example 2: Trig 1, 50% = False > Trig 2, PRE = True > Trig 3, PRE = True > Trig 4, PRE = False
NEI is true and the trig plays if the most recently evaluated trig condition on the neighbor track was
true. (PRE and PRE conditions on the neighbor track are not evaluated and are ignored) The neighbor
track is the track before the active track. For example, track 3 is the neighbor track of track 4. NEI
and NEI conditional trigs on track 4 therefore evaluate the conditional trigs placed on track 3. The NEI
condition is false if no conditional trigs exist on the neighbor track.
NEI (Not NEI) is true when NEI is false. A trig with this trig condition plays if the most recently evaluat-
ed trig condition on the neighbor track was not true. (PRE and PRE conditions on the neighbor track
are ignored and not evaluated.)
1ST The trig plays only on the first loop of the pattern.
1ST (Not 1ST) The trig does not play on the first loop of the pattern.
LST The trig plays the last time the pattern plays before changing to another pattern.
LST (Not LST) The trig does not play the last time the pattern plays before changing to another pattern.
A:B A sets how many times the pattern (or track, if the track length is shorter than the pattern length)
plays before the trig condition is true. B sets how many times the pattern (or track, if the track length
is shorter than the pattern length) plays before the count is reset and starts over again. This cycle
then repeats until you stop the sequencer.
For example:
With the setting 1:2, the trig plays the first time the pattern plays and then the third, the fifth, and so on.
With the setting 2:2, the trig plays the second time the pattern plays and then the fourth, the sixth, and
so on.
With the setting 2:4, the trig plays the second time the pattern plays and then the sixth, the tenth, and
so on.
With the setting 4:7 the trig plays the fourth time the pattern plays and then the eleventh, the eigh-
teenth, and so on.
A:B (Not A:B) is true when A:B is false. Meaning a trig with this condition plays when the set condition
of pattern plays are not met.
For example:
With the setting 2:4, the trig does not play the second time the pattern plays and then not on the sixth,
the tenth, and so on.

```
Conditional parameter locks are a great way to add variety to a pattern. To set up a grid
of mutually exclusive or conclusive note trigs across any of the tracks, and adding some
probability locks in there as well (perhaps, in turn, also sporting an array of logically con-
ditioned trigs), is a neat way to make the most minimal of compositions come alive with
randomness.
```

##### 10. THE SEQUENCER

- The FILL trig condition has a separate parameter on TRIG PAGE 1. For more informa-
    tion, please see “11.2 TRIG PAGE 1” on page 55.
- The sequencer needs to be in FILL mode to activate the conditional lock called FILL.
    For more information, please see “10.12.4 FILL MODE” on page 50.
- FILL trig conditions may also be used, for example, to have two different melodic or per-
    cussive sequences on the same track, one of which is activated only when FILL mode is
    active.

#### 10.12.4 FILL MODE

```
FILL mode can be used to create a temporary variation, such as a drum fill, in your pattern.
Press [YES] + [PAGE] to activate FILL mode for one pattern cycle. It will become active when the
pattern loops, and remain active until it loops again. You can also activate FILL mode at any time, and
for any duration, by pressing and holding the [PAGE] key when the pattern is playing (when not in GRID
RECORDING mode). The FILL mode is active for as long as the key is held.
Press and hold [PAGE] + [YES], and then release [PAGE] before you release [YES] to latch FILL mode.
Press [PAGE] again to unlatch FILL mode.
```
#### 10.12.5 COPY, PASTE AND CLEAR OPERATIONS

```
Patterns, tracks, track pages, parameter pages, and trigs can be copied, pasted and cleared.
The active pattern can be copied to another location in the same bank or in another bank. To perform
a pattern copy operation, GRID RECORDING mode must be deactivated. Copy the pattern by pressing
[FUNC] + [RECORD]. Select another pattern, and then press [FUNC] + [STOP] to paste the copied
pattern to this location. Press [FUNC] + [PLAY] to clear all the trigs in the current pattern.
Individual sequencer tracks can be copied, pasted and cleared in the same way as patterns. To do so,
GRID RECORDING mode must be active.
A single track page may also be copied, pasted and cleared. It is similar to copying/pasting/clearing a
track, but will only affect the active track page. Select the track page of choice by pressing the [PAGE]
key. Copy by pressing [PAGE] + [RECORD]. Select a new track page and press [PAGE] + [STOP] to
paste. Press [PAGE] + [PLAY] to clear the active track page.
A single parameter page with all it's settings may also be copied, pasted and cleared. Select the pa-
rameter page you wish to copy by pressing a [PARAMETER] key. Copy by pressing [PARAMETER] +
[RECORD]. Select a new track and then press the same [PARAMETER] page + [STOP] to paste. Press
[PARAMETER] + [PLAY] to clear the parameter page.
Trigs, complete with all parameter lock settings, can be copied, pasted and cleared as well. GRID
RECORDING mode needs to be active to access this functionality. Press and hold a trig and press
[RECORD] to perform the copy operation. Paste by holding another [TRIG] key and pressing [STOP].
It is also possible to copy more than one trig. Press and hold the trigs you wish to copy, and then press
[RECORD]. Press and hold the [TRIG] key to where you want to paste, and then press [STOP] to paste
the copied sequence of trigs. When pasting, the copied trigs are placed in the same relation to each
other as they had when they were copied. The destination trig acts as the starting point for the sequence
of copied trigs. Clear trig locks by holding one or more trigs and press [PLAY].
You can undo any copy, paste, and clear operation by repeating the key presses.
```
```
It is possible to copy, clear and paste one or several patterns without leaving the active
pattern. First press [PTN], then press [TRIG] + [RECORD] to select the pattern you wish
to copy and copy it. Then press [TRIG] + [STOP] to paste the copied pattern to where you
wish to paste it. Press [TRIG] + [PLAY] to clear the selected pattern.
```
#### 10.12.6 TEMPORARY SAVE AND RELOAD PATTERN COMMANDS

```
Patterns can instantly be saved to a temporary memory space and then reloaded again from this space.
```
- Press [FUNC] + [YES] to perform a temporary save of the active pattern. This is useful in order to cre-
    ate a restore point when you are live tweaking, but without permanently save your changes.
- Press [FUNC] + [NO] to reload a pattern that was previously temporarily saved. It will reload the pat-
    tern from when you last used the temporary save command. If you have not done any temporary save,
    then the pattern will reload from the permanently saved state.

```
The TEMPORARY SAVE PATTERN and TEMPORARY RELOAD PATTERN commands are
great when improvising live. Any changes made to the active pattern, like adding bass line
notes or using CONTROL ALL, can immediately be undone. And at the same time, the
saves and reloads will not affect the pattern in a permanent way.
```

##### 10. THE SEQUENCER

```
The TEMPORARY SAVE PATTERN command will not save the pattern permanently and
any changes will be lost if you load another project. If you wish to save your changes per-
manently, you should use SAVE TO PROJ in the PATTERN MENU. For more information,
please see “13.3 PATTERN MENU” on page 71.
```
#### 10.12.7 TRACK TRANSPOSE

1. Press and hold [FUNC] and then press [+]/[−] to initiate transpose of the active track in semitones.
    Press and hold [TRK] + [FUNC] and then press [+]/[−] to initiate transpose of the active track in
    octaves.
2. Release [FUNC] (or [TRK] + [FUNC]) for the transpose to take effect.

#### 10.12.8 PATTERN TRANSPOSE

1. Press and hold [PTN] and then press [+]/[−] to initiate transpose of all the tracks in the pattern in
    semitones.
    Press and hold [PTN] + [FUNC] and then press [+]/[−] to initiate transpose all the tracks in the
    pattern in octaves.
2. Release [PTN] (or [PTN] + [FUNC]) for the transpose to take effect.
You can selects which tracks that will be affected by pattern transpose. For more information, please
see “9.6.1 KIT” on page 34.

#### 10.12.9 SELECTED TRACKS TRANSPOSE

```
You can also select which tracks in the pattern are affected by the transposition. This is set up similarly
to the CONTROL ALL functionality.
```
1. Press [FUNC] + [FLTR] to open the SETUP menu. Use the [UP] and [DOWN] arrow keys to select
    TRANSPOSE ALL CONFIG. Press [YES] to confirm your selection.
2. Use the [TRIG] keys to select which tracks that you want to be affected by the transposition. MIDI
    tracks can not be selected.
3. Press and hold [TRK] and then press [+]/[−] to initiate transpose of all the selected tracks in the
    pattern. The tracks are transposed up/down in semitones.
4. Release [TRK] for the transpose to take effect.

```
These transpositions are non-destructive and does not change the trigs NOTE values.
```
### 10.13 SONG MODE

A song is an arrangement of patterns set up to play in sequence. Each row in the SONG mode arrangement
can have separate settings for pattern, row repeat, row length, tempo, and mute. A song can be up to 99
rows in length, and each project can contain up to 16 songs.

#### 10.13.1 THE SONG EDIT SCREEN

```
Press [SONG] and then press [TRIG 1–16] to select a song and enter SONG mode. Press [FUNC] +
[SONG] to open the SONG EDIT screen. Use the [ARROW] keys to navigate the song rows and col-
umns, highlighting the parameter you wish to edit, and then use LEVEL/DATA or any DATA ENTRY knob
to edit the selected function.
```

##### 10. THE SEQUENCER

```
1 7
```
```
10
```
```
11
```
```
12
```
```
2 3 4 5 6 8
```
```
9
```
1. SONG ROW (Range 01–99)
2. LABEL lets you select a keyword for the row. The keyword is related to song structure, e.g., Verse,
    Chorus, and Fill. It can also be the name of the pattern.
3. PTN lets you select the pattern to be played on the row. Press [FUNC] while turning the knob to
    change banks quickly.
4. ROW PLAY COUNT The number of times the row plays before the song advances to the next row.
5. ROW LENGTH (measured in sequencer steps). This setting defines how many steps the sequenc-
    er plays from the selected pattern. The default value is the same as the pattern length. (Range:
    2–1024, the last 25 values are written as K00–K24)
6. ROW TEMPO Here, you set the row’s BPM per row. You can also choose to have one BPM for the
    whole song. By default, the row inherits the BPM and swing set in the pattern selected for the row.
    Press [TEMPO] to open the TEMPO menu for the currently selected row’s pattern. Here you can
    also set the swing. You can also press [YES] while highlighting the BPM parameter to open the
    TEMPO menu. Selecting song tempo on any row overrides all the previously set row and pattern
    tempos. The swing settings are always set per row.
7. SONG POINTER Shows the current time and position of the playhead in the song arrangement.
8. ROW MUTE lets you mute the tracks of the pattern on the selected row. Highlight and press [YES]
    to edit. A mute icon is displayed in the rows that have tracks that are muted. Use the [TRIG] keys
    to mute and unmute the tracks. When selecting a pattern for the row, the row’s mute state initially
    reflects the pattern’s mute state
9. END row. This row is always added at the end of a song and determines what will happen when the
    song has played the last song row. By default, it is set to LOOP the song from the beginning and
    play it again, but it can also be set to STOP the song.
10. The currently selected row is highlighted between dotted lines. Use [UP] and [DOWN] to select row.
11. PLAYHEAD position shows the row currently playing (or set to play if the sequencer is stopped).
12. SONG SLOT and SONG NAME.

#### 10.13.2 CREATING AND EDITING A SONG

1. Press [SONG] and then press [TRIG 1–16] to select a song and enter SONG mode.
2. Press [FUNC] + [SONG] to open the SONG EDIT screen.
3. If you selected an empty song slot, you are presented with a screen where you can choose:
    INSERT ROW Inserts the first row in the song. Press [FUNC] + [DOWN] to insert a row.
    CREATE ROWS FROM CHAIN Creates a song based on a chain. Press [YES] and then select to
    create rows from a previously created chain or to create a new chain by selecting patterns using
    the [TRIG 1–16] keys. Use [LEFT]/[RIGHT] to change bank. Then press [YES] to create a song
    based on the chain and open the SONG EDIT screen.
4. Press [FUNC] + [DOWN] if you want to add a new row to the song arrangement. The new row is
    added below the currently selected row and is a copy of the selected row. Press [FUNC] + [UP] to
    remove the selected row from the song arrangement.
5. Use the [ARROW] keys to navigate the song rows and columns, highlighting the item you wish
    to edit. For more information about editing the functions in SONG mode, please see “10.13.1 THE
    SONG EDIT SCREEN” on page 51.
6. To rename the song, navigate to SETTINGS > SONG > RENAME, and then edit the song name.
7. Press [NO] or [FUNC] + [SONG] to exit the SONG EDIT screen.


##### 10. THE SEQUENCER

- [FUNC] + [RECORD] copies the selected row.
    [FUNC] + [STOP] pastes a previously copied row to the selected row.
    [FUNC] + [PLAY] resets the selected row to the pattern’s default BPM, length, and mute
    state settings.
    [FUNC] + [UP] deletes the selected row.
- Please note that there is always a row called END at the song’s end. This row can be set
    to either STOP the song or LOOP it from the beginning and play it again.
- On the SONG EDIT screen, you can select a song row number and then press YES to
    move the playhead to this row. You can also use the [TRIG 1–16] keys to preview and
    play the track presets from the pattern on the playhead row.

#### 10.13.3 PLAYING A SONG

1. Press [SONG] and then press [TRIG 1–16] to select a song and enter SONG mode. The song selected
    will be the one that was last played or edited.
2. Press [PLAY] to play the selected song. At the top of the screen, you see the song number, the row
    currently playing, the total number of rows in the song, the row’s LABEL, and finally the patterns bank/
    number. This part of the screen is also used as a progress bar to show how far in the row the song
    pointer has progressed.
3. Press [STOP] to stop the playback. You can then press [PLAY] to continue to play the song from the
    current playhead position. Press [STOP] twice to move the playhead to the song’s beginning.
4. To exit song mode, press [PTN] + [TRIG 1–16] to return to pattern mode.
    - Press [SONG MODE] + [LEFT] (when in SONG mode) to loop the currently playing row.
       Press [SONG MODE] + [LEFT] again (when in SONG mode) to stop looping the row and
       return to normal song playback.
    - Press [SONG MODE] + [UP]/[DOWN] (when in SONG mode) to select a specific song row
       to jump to and to play next.
    - Press [PTN] and the [TRIG 1–16] keys to select a pattern to exit SONG mode and return to
       regular pattern play.

```
The songs are automatically saved as they are created and edited. However, you must save
the project if you wish to keep the songs before switching to another project.
```
### 10.14 PERFORM KIT MODE

In PERFORM KIT mode, any changes made to the preset parameters are not auto-saved, and kits are not
loaded when you change the pattern; instead, you keep the previous (tweaked) kit. It means you can keep
your parameter tweaks over several patterns and have a smooth evolving performance without having the
kits reloaded or inadvertently saving your kit when you change patterns.

Press [FUNC] + [PRESET/KIT] to toggle PERFORM KIT mode on/off. When PERFORM KIT mode is on, a
flashing “P” is shown next to the pattern name on the screen and the [PRESET/KIT] key is lit orange.


##### 10. THE SEQUENCER

#### 10.14.1 PERFORM KIT MODE ACTIONS

- Press [PRESET/KIT] + [NO] to reload the current patterns kit instantly.
- Press [PRESET/KIT] + [YES] to save the current patterns modified kit. This will open the SAVE
    (KIT) menu. For more information please see “9.2.6 SAVE (KIT)” on page 32.

```
Pattern Temp save and Temp reload still works while in PERFORM KIT mode
```
```
Actions done in PERFORM KIT mode are not saved and will not remain when switching the
device off. Saving the pattern will not save any changes done in PERFORM KIT mode.
```

## 11. TRACK PARAMETERS

11. TRACK PARAMETERS

Here follows a description of all of the parameters that are available on the audio tracks PARAMETER
pages. The parameters on the TRIG page are not saved together with the preset, but are instead saved
with the pattern. The track parameters may be locked to other settings on any step of the pattern by first
pressing and holding a [TRIG] key, then changing the parameters’ settings with the DATA ENTRY knobs.
For more information, please see “10.12.1 PARAMETER LOCKS” on page 48.

```
Please note that MIDI tracks have a different set of parameters on the TRIG, SYN, FLTR, and
AMP pages. For more information, please see “A.2 SYN MACHINES” on page 87.
```
### 11.1 EDITING THE TRACK PARAMETERS

There are six PARAMETER pages for the tracks. Press the [PARAMETER] keys to access the tracks
PARAMETER pages. Use the [UP]/[DOWN] keys to access the parameter group's pages. You can also
press a [PARAMETER] key repeatedly to cycle trough the parameter pages in that group. Press and hold a
[PARAMETER] key to see the values for all parameters on that page.

- You can always reload a preset from its last saved state. Press [TRK] + [TRIG 1–16] + [NO]
    to reload the preset.
- You can also randomize the parameter settings on a specific PARAMETER page on an
    audio track. Press [PARAMETER] key + [YES] to randomize the relevant parameters on that
    page. Every time you press this key combination, the parameters randomize in a new way.
- Press [PARAMETER] key + [NO] to reset the PARAMETER page to its last saved state.

### 11.2 TRIG PAGE

Here you set the options for notes that are trigged. This is also where you select trig conditions. Press the
[TRIG PARAMETERS] key to access the menu. Change settings using the DATA ENTRY knobs. These
general settings affect note trigs placed on the sequencer.

##### NOTE

```
Trig Note sets the pitch of the note when trigged. When in LIVE RECORDING mode and playing in KEY-
BOARD mode, the pitch of the [TRIG] keys played will override this setting. Press and turn DATA ENTRY
knob A to select only note values that exist in the scale set by KB SCALE. This behavior can be inverted
using the NOTE PARAM setting in the PERSONALIZE menu. For more information, please see “13.7.6
NOTE PARAM” on page 77 and “8.5.2 KEYBOARD SETUP MENU” on page 24.
```
```
VEL
Trig Velocity sets the velocity of the sequencer’s note trigs.
```
```
LEN
Trig Length sets the length of the note trig. In LIVE RECORDING mode, the duration of pressing the
[TRIG] keys overrides this general setting.
```
```
PROB
Trig Probability sets the probability that the trigs on the track plays or not. The probability outcome is
re-evaluated every time a trig is set to play. The default setting is 100%, meaning that all the trigs on the
track plays every time. This parameter can be parameter locked which lets you give separate trigs their
own probability.
```
```
L F O.T
LFO Trig controls if the LFO will be trigged or not.
```

##### 11. TRACK PARAMETERS

##### F LT.T

```
Filter Trig controls if the filter envelope will be trigged or not.
```
```
FILL
Fill is a separate trig condition that determines if a trig is active (plays) or not depending on if the device
is in FILL mode or not. For more information, please see “10.12.4 FILL MODE” on page 50, and “10.12.3
TRIG CONDITIONS AND CONDITIONAL LOCKS” on page 49.
ON, A trig with FILL set to ON, plays when FILL mode is active.
OFF, A trig with FILL set to OFF plays when FILL mode is not active.
```
```
The sequencer needs to be in FILL mode to activate the FILL trig condition. For more
information, please see “10.12.4 FILL MODE” on page 50.
```
##### COND

```
(Trig Condition) when you add a conditional lock, COND sets the Trig Condition with which a set of con-
ditional rules can be applied to any trig, using a conditional parameter lock. For more information, please
see “10.12.3 TRIG CONDITIONS AND CONDITIONAL LOCKS” on page 49.
```
### 11.3 TRIG PAGE

```
Here you set the behavior of retrigs that are added to note trigs placed in the sequencer. On this page you
also control the portamento. Change settings using the DATA ENTRY knobs. These general settings all
affect note trigs placed in the sequencer.
Retrigs can be customized on any of the sequencer steps on the audio tracks (the retrig function is not
available on the MIDI tracks). In GRID RECORDING mode, press [TRIG PARAMETERS] twice to access the
RETRIG PARAMETERS page. Hold one or several [TRIG] keys and then set the desired retrig options for
the trig(s). The RETRIG PARAMETERS page shows the retrig actions for the chosen sequencer step(s) on
the active track. The retrig settings are stored in the active pattern.
```
##### RTRG

```
Retrig enables a number of trig repeats on placed trigs. Use DATA ENTRY knob A to turn retrig on/off.
For information on setting retrig locks on specific trigs in the sequencer, please see “10.9 RETRIGS” on
page 46.
```
```
VFAD
Velocity Fade sets the velocity curve fade out/fade in of the retrig (-64–64). -64 corresponds to a
complete fade out during the set length, -32 fades out to half the velocity during the set length, 0 equals
a flat velocity curve with no fade, 32 fades into half velocity during the set length and 64 fades in com-
pletely to full velocity during the set length. Please note that the effects of the VFAD setting is also
dependent on the VEL setting on TRIG PAGE 1.
```
```
LEN
Length sets the duration of the retrig velocity curve in fractions of, or rational or integer multiples of, a
step (0,125–INF). 1/16 is the nominal length of one step. This setting affects the behavior of the velocity
curve by defining the boundaries of its envelope.
```
```
R AT E
Rate sets the retrig rate. 1/16 is the nominal retrig rate, one trig per step. 1/32 corresponds to two trigs
per step and so on. To do triplets, for example, set the retrig rate to 1/12 (or 1/24).
```

##### 11. TRACK PARAMETERS

##### PTIM

```
Portamento Time sets the time for the portamento.
```
```
PORT
Portamento turns the portamento on/off. Please note that this parameter is only available on audio
tracks.
For more information, please see “9.6.2 TRACK” on page 35.
```
### 11.4 SYN PAGES

The parameters on the SYN pages control the selected SYN machine, and these parameters vary depend-
ing on what machine is selected. The SYN machines are different synthesis engines with unique functional-
ity. In the SYN machine section you also find the MIDI machine that lets you control external, MIDI equipped,
gear. You can assign any SYN machine to any track. For more information, please see “A.2 SYN MACHINES”
on page 87.

Press [SYN] to access these parameter pages.

The screen shot shows the first page for the FM Tone machine.

### 11.5 FLTR PAGE

The parameters on the FLTR page 1 control the filter, and these parameters vary depending on what FLTR
machine is selected. The FLTR machines are a collection of different filters and EQ. On this page you also
find the filter envelope. For more information, please see “A.3 FLTR MACHINES” on page 100.

Press [FLTR] to access this parameter page.

The screen shot shows the page for the Multi-mode FLTR machine.

### 11.6 FLTR PAGE

On FLTR page 2, you will find the parameters that control the base-width filter and a number of parameters
that affects the selected filter machine on FLTR page 1.

The base-width filter is basically a Highpass filter and a Lowpass filter connected in tandem. The filters
BASE and WDTH parameters define the base-width filters frequency range.


##### 11. TRACK PARAMETERS

```
Examples of how the BASE and WDTH parameters affect the filter frequency range:
```
##### FREQUENCY

##### AMPLITUDE

```
Base
```
```
Width
```
##### FREQUENCY

##### AMPLITUDE

```
Base
```
```
Width
```
##### FREQUENCY

##### AMPLITUDE

```
Base
```
```
Width
```
- With BASE set to 0, the filter functions as a low pass filter with WDTH adjusting the fre-
    quency range.
- With WDTH set to 127, the filter functions as a high pass filter with BASE adjusting the
    frequency range.
- With WDTH set to 0, the filter functions as a band pass filter with BASE adjusting the
    frequency.
- With BASE set to 0 and WDTH set to 127 the filter does not affect the sound.

```
DEL
Envelope delay sets the time before the attack phase of the filter envelope starts. This parameter is part
of the filter envelope that controls the FLTR machine. You can also press and hold [FUNC] and then turn
DATA ENTRY knob A to quickly access this parameter from FLTR page 1. This parameter only affects the
selected filter machine on FLTR page 1.
```
```
KEY.T
Keytrack sets how much the filter cutoff frequency is affected by what note is playing. The higher the
KEY.T setting, the more the filter opens up at higher notes. This parameter only affects the selected filter
machine on FLTR page 1.
```
```
BASE
Sets the base frequency of the filter.
```
```
WDTH
Sets the frequency width above the base frequency.
```
```
BW.RT
BW Routing selects if the base-width filter should be routed before (Pre) or after (Post) the machine
selectable filter in the audio path.
```
```
RSET
Filter Envelope Reset sets the filter envelope behavior. This parameter only affects the selected filter
machine on FLTR page 1.
ON resets the envelope for each consecutive trig (default). The envelopes attack phase is reset to zero.
OFF does not reset the envelope for each consecutive trig. The envelopes attack phase continues
from where it was left off.
```
### 11.7 AMP PAGE

```
The AMP page controls parameters for the amplitude envelope, panning, and
volume.
Press [AMP] to access this parameter page.
```

##### 11. TRACK PARAMETERS

##### AT K

Attack Time sets the length of the attack phase of the amp envelope.

HOLD

Hold Time sets the length of the hold phase of the amp envelope. Fixed Hold time values (0–126) specify
the length of the hold phase, and the envelope ignores Note Off events such as Trig Length, releasing a
[TRIG] key or a key on an external controller. Setting HOLD to NOTE means the hold phase will be deter-
mined by Note On and Note Off events. This parameter is only available if MODE is set to AHD.

```
If you set HOLD to NOTE and use an external keyboard to trigger the envelope, then the
sound will be sustained (if DEC is set to less than 127) for as long as you press a key on the
keyboard.
```
DEC

Decay Time sets the length of the decay phase of the amp envelope.

SUS

Sustain Level sets the sustain level of the amp envelope. This parameter is only available if MODE on is
set to ADSR.

REL

Release Time sets the length of the release phase of the amp envelope. This parameter is only available
if MODE is set to ADSR.

RSET

Amp envelope reset sets the amp envelope behavior:

```
ON resets the envelope for each consecutive trig (default). The envelopes attack phase is reset to zero.
OFF does not reset the envelopes for each consecutive trig. The envelopes attack phase continues
from where it was left off..
```
MODE
Envelope mode sets the amplitude envelope to function as an AHD or an ADSR envelope.

PAN

Pan positions the audio in the stereo field.

VOL

Volume sets the volume amount of the amplifier. Unlike the track LEVEL, this parameter can be parame-
ter locked. and is saved with the preset.

Amplitude envelope with MODE set to AHD and a fixed HOLD time.

##### TIME

##### AMPLITUDE

```
Note On
```
```
Attack Hold Decay
```
```
Note Off
```
```
Attack Hold Decay
```
```
Note On Note Off
```
```
Attack Hold Decay
```
```
Note OnNote Off
```

##### 11. TRACK PARAMETERS

```
Amplitude envelope with MODE set to AHD and HOLD set to NOTE.
```
##### TIME

##### AMPLITUDE

```
Note On
```
```
Attack
Hold Decay
```
```
Note Off
```
```
Attack Decay
Hold
```
```
Note
On
```
```
Note Off
```
```
Attack/
Hold
```
```
Decay
```
```
Note On Note Off
```
### 11.8 FX PAGE

```
The FX parameter page contains a number of FX related parameters such as the Bit Reduction, Overdrive,
and Sample Rate Reduction. Here you also find the send levels for the Delay, Reverb, and Chorus effects.
Press [FX] to access this parameter page.
```
##### BR

```
Bit Reduction sets the bit depth. The parameter range is from 16 bits to 1 bit.
```
```
OVER
Overdrive sets the gain amount into the digital overdrive.
```
```
SRR
Sample Rate Reduction sets the amount of sample rate reduction.
```
```
SR.RT
SRR Routing sets if the Sample Rate Reduction effect is routed before (PRE) or after (POST) the filter
machine.
```
```
DEL
Delay Send sets the amount of sound sent through to the Delay effect. For more information, please see
“12. FX AND MIXER PARAMETERS” on page 63.
```
```
REV
Reverb Send sets the amount of sound sent through to the Reverb effect. For more information, please
see “12. FX AND MIXER PARAMETERS” on page 63.
```
```
CHR
Chorus Send sets the amount of sound sent through to the Chorus effect. For more information, please
see “12. FX AND MIXER PARAMETERS” on page 63.
```
```
OD.RT
Overdrive Routing selects if the overdrive effect is routed before (PRE) or after (POST) the filter machine.
```
```
Please note that there are no parameters on the FX page for MIDI tracks.
```

##### 11. TRACK PARAMETERS

### 11.9 MOD PAGE

The MOD pages contain three LFOs (two for tracks that use MIDI machines). The Low-Frequency Oscilla-
tor can be used to modulate track parameters. Customize the low-frequency oscillator behavior, orientation,
speed, and depth on this page. This page controls the behavior of LFO 1

Press [MOD] to access this parameter page.

##### SPD

```
Speed sets the speed of the LFO. Try settings of 8, 16 or 32 to sync the LFO to straight beats. The knob
is bipolar. The LFO cycle can be played backward by using negative values.
```
```
MULT
Multiplier multiplies the SPD parameter by the set factor either by multiplying the current tempo (BPM
settings), or by multiplying a fixed tempo of 120 BPM.
```
```
FADE
Fade In/Out makes it possible to fade in/fade out the LFO modulation. The knob is bipolar. Positive val-
ues give a fade-out, negative values give a fade in. 0 gives no fade in/fade out. (-64–63)
```
```
DEST
Destination selects the modulation destination for the LFO. Preview how the LFO modulation will affect
the sound by highlighting a destination. Press [YES] to confirm the selection. Press [NO] to cancel and
revert to previous selection For more information, please see “APPENDIX D: LFO MODULATION DESTI-
NATIONS” on page 117
```
```
WAV E
Waveform sets the LFO waveform. The Triangle, Sine, Square, Sawtooth, and Random waveforms are
bipolar. The Exponential and Ramp are unipolar.
```
```
SPH
Start Phase sets the point within the wave cycle where the LFO will start when it is trigged. 0 makes the
LFO start at the beginning of a complete wave cycle, 64 makes it start at the center. A small square at
the start of the waveform shows that the wave cycle starts at a zero-crossing. If WAV is set to RND then
the SPH parameter changes to SLEW and adds slew to the transitions in the waveform.
```
```
MODE
Trig Mode sets how the LFO will act when a note is trigged.
```
- FREE is the default free-running mode. It makes the LFO run continuously, never restarting or
stopping even if notes are trigged.
- TRIG makes the LFO restart when a note is trigged.
- HOLD makes the LFO run free in the background, but when a note is trigged the LFO output level is
latched and held still until the next note is trigged.
- ONE The LFO starts when a note is trigged, then runs to the end of the waveform and then stops.
This makes the LFO function similar to an envelope.
- HALF The LFO starts when a note is trigged, then runs to the middle of the waveform and then stops.

```
DEP
Depth sets the depth and polarity of the LFO modulation. Both negative and positive modulation depth is
possible. A center setting, 0.00, equals no modulation depth.
```

##### 11. TRACK PARAMETERS

### 11.10 MOD PAGE

```
MOD page 2 page contains the same parameters as LFO page 1, but controls the behavior of LFO 2.
```
### 11.11 MOD PAGE

```
MOD page 3 page contains the same parameters as LFO page 1, but controls the behavior of LFO 3.
```
```
Please note that MIDI tracks only have two LFOs.
```
```
LFO waveforms and trig modes.
```
```
TRIG
```
```
HALF
```
```
TRIG
```
```
ONE
```
```
TRIG TRIG
```
```
HOLD
```
```
TRIG TRIG
```
```
TRIG
```
```
TRIG TRIG
```
```
FREE
```
```
TRI
```
```
SINE
```
```
SQR
```
```
SAW
```
```
EXPO
```
```
RAMP
```
```
RAND
```
```
LFO TRIG MODE
```
```
WAVEFORM
```
```
The table below shows the LFO speed measured in sequencer steps using different combinations of SPD
and MULT (set to a BPM value) settings. For example, a value of 8 in the table means that the LFO will do
one full cycle in the time it takes the sequencer to advance eight steps.
```
### MULT

### S

### P

### D

##### 1 2 4 8 16 32 64 128 256 512 1K 2K

##### 1 128 64 32 16 8 4 2 1 1/2 1 /4 1/8 1/16

##### 2 64 32 16 8 4 2 1 1/2 1 /4 1/8 1/16 1/32

##### 4 32 16 8 4 2 1 1/2 1 /4 1/8 1/16 1/32 1/64

##### 8 16 8 4 2 1 1/2 1 /4 1/8 1/16 1/32 1/64 1/128

##### 16 8 4 2 1 1/2 1 /4 1/8 1/16 1/32 1/64 1/128 1/256

##### 32 4 2 1 1/2 1 /4 1/8 1/16 1/32 1/64 1/128 1/256 1/512

##### 64 2 1 1/2 1 /4 1/8 1/16 1/32 1/64 1/128 1/256 1/512 1/1024


## 12. FX AND MIXER PARAMETERS

12. FX AND MIXER PARAMETERS

The Digitone II effects and mixers parameters are explained in this chapter.

### 12.1 EDITING THE SEND FX AND MIXER PARAMETERS

The Digitone II’s Chorus, Delay and Reverb are send effects and are set on a pattern level. It means that all
the presets in a pattern shares the same effect settings but have individual send levels to the effects. The
Delay, Reverb, and Chorus parameters are set on their respective PARAMETER page, but their incoming
signals are set by the DEL, REV, and CHO send parameters on the FX page of each audio track.

Press [FUNC] + [FX] to access the SEND FX parameter pages, and then use the [UP]/[DOWN] keys to
navigate between the sub pages. Use the DATA ENTRY knobs A-H to change the parameters.

Press [FUNC] + [MOD] to access the MIXER parameter pages, and then use the [UP]/[DOWN] keys to
navigate between the sub pages. Use the DATA ENTRY knobs A-H to change the parameters.

```
The FX and MIXER parameter settings are stored as part of the pattern. Don’t forget to save
the pattern and give it a unique name once you have achieved the results you want.
```
### 12.2 DELAY

##### (SEND FX PAGE 1)

The Delay send effect takes the input signal, delays it in time and then rejoins it with the original signal.

##### TIME

```
Delay Time sets the delay time. It is relative to the current BPM and is measured in 128th notes.
```
```
TIME setting Divide ratio
1 1/128
2 1/64
2.67 1/48 (1/32T)
3 1/64.
4 1/32
5.33 1/24 (1/16T)
6 1/32.
8 1/16
10.67 1/12 (1/8T)
12 1/16.
16 1/8
21.33 1/6 (1/4T)
24 1/8.
32 1 /4
42.67 1/3 (1/2T)
48 1/4.
64 1/2
96 1/2.
128 1
```

##### 12. FX AND MIXER PARAMETERS

##### X

```
Ping-pong sets the delay signal to alternate across the stereo field. There are two settings:
```
- OFF lets you manually set the position of the delay signal in the stereo field. Use the WID parameter
to change the stereo field position.
- ON makes the delay signal alternate between left and right pan positions. The WID parameter
controls the amount of panning.

```
WID
Stereo Width sets the delay signal pan width across the stereo field. The knob is bipolar.
```
```
FDBK
Feedback Gain sets the amount of delay output signal to feed back into the input of the delay. With high-
er parameter settings, infinite and/or swelling delays are possible. Please be aware that high feedback
can lead to a very loud signal.
```
```
VOL
Delay Volume sets the volume of the Delay output signal. Use LEVEL/DATA to set this parameter.
```
```
HPF
HPF sets the cutoff frequency of the delay highpass filter.
```
```
LPF
LPF sets the cutoff frequency of the delay lowpass filter.
```
```
REV
Reverb Send sets the amount of Delay output signal to be sent to the Reverb.
```
### 12.3 REVERB

##### (SEND FX PAGE 2)

```
The Reverb send effect controls the persistence, and ambient characteristics, of the sound reverberations.
It can simulate many different sonic locations, from huge spaces to small rooms.
```
##### PRE

```
Pre-delay sets the pre-delay time of the Reverb.
```
```
DEC
Decay Time sets the length of the decay phase of the reverberated signal, essentially setting the size of
the simulated acoustic space.
```
```
FREQ
FB Shelving Freq sets the shelving filter frequency. Together with the GAIN parameter, it can be used to
dampen the reverberated signal above a chosen frequency, making the reverberation sound more promi-
nent or more muffled.
```
```
GAIN
FB Shelving Gain affects the damping of the reverberated signal above the shelving frequency set by the
FREQ parameter. At max value the treble is included in the reverberations; lowering the value gradually
dampens it.
```
```
VOL
Reverb Volume sets the volume of the Reverb output signal. Use LEVEL/DATA to set this parameter.
```

##### 12. FX AND MIXER PARAMETERS

##### HPF

```
HPF sets the cutoff frequency of the reverb high-pass filter that affects the audio going into the reverb.
```
```
LPF
LPF sets the cutoff frequency of the reverb low-pass filter that affects the audio going into the reverb.
```
### 12.4 CHORUS

##### (SEND FX PAGE 3)

The chorus can be used to widen sounds, enhance the stereo image or add subtle movement to sounds.

##### DPTH

```
Depth sets the depth of the LFO modulation of the chorus.
```
```
SPD
Speed sets the speed of the LFO modulation of the chorus.
```
```
HPF
High-pass filter sets the highpass filtering of the input signal.
```
```
WDTH
Width sets the stereo width of the chorus.
```
```
VOL
Chorus Volume sets the volume of the Chorus output signal. Use LEVEL/DATA to set this parameter.
```
```
DEL
Delay Send sets how much of the wet chorus signal to send through to the delay.
```
```
REV
Reverb Send sets how much of the wet chorus signal to send through to the reverb.
```
### 12.5 COMPRESSOR

##### (MIXER PAGE 1)

The Compressor master effect compresses the dynamic range of the signal by reducing the volume of loud
sounds relative to the quiet sounds. There are eight parameters with which the compressor is customized
on this page. There is a bar on the left side of the screen that visually represents the amount gain reduction

##### THR

```
Threshold sets the threshold of the compressor. A lower threshold means a larger portion of the signal is
subject to compression.
```

##### 12. FX AND MIXER PARAMETERS

##### AT K

```
Attack sets the time of the compressor attack phase, i.e. how quickly the compressor responds to loud
peaks.
```
```
REL
Release sets the time of the compressor release phase, i.e. how long it takes for the compression to
recover in quieter moments.
```
```
MUP
Makeup Gain sets the makeup gain of the compressor output, to compensate for the reduced signal
levels caused by compression. The Makeup Gain parameter value is displayed in dB.
```
```
VOL
Pattern Volume sets the kits overall audio level. This parameter basically has the same function as the
MAIN VOLUME knob, but is saved together with the kit. Use LEVEL/DATA to adjust the settings.
```
```
R AT
Compression Ratio. There are eight different compression ratios, 1.50, 2.00, 3.00, 4.00, 6.00, 8.00, 16.00
and 20.00. Higher ratios result in greater compression of the signal.
```
```
SCS
Sidechain Source decides what audio source the compressor analyzes when it performs sidechaining.
Sidechaining is the process when the compressor uses the output of an audio source to control the
overall action of the compressor. For example, if you set SCS to TRK1 where you have a kick drum, every
time the kick drum plays, the compressor lowers the overall sound of the mix.
COMP MIX sets the sidechain source to be all the tracks that are routed to the compressor.
COMP (Not Comp) sets the sidechain source to be all the tracks that are not routed to the compressor.
TRK1–16 sets the sidechain source to be the sound sent from one of the separate audio tracks.
IN LR sets the sidechain source to be the sound coming from IN L/R.
IN L sets the sidechain source to be the sound coming from INPUT L.
IN R sets the sidechain source to be the sound coming from INPUT R.
```
- The results of the COMP MIX and NOT COMP settings depends on the selections
    you make on the COMPRESSOR ROUTING page. For more information please see
    “9.6.1 KIT” on page 34.
- The IN L and IN R options are only available if you set DUAL on the External mixer
    page to ON. The IN LR option is only available if you set DUAL to OFF. For more infor-
    mation, please see “12.9 EXTERNAL MIXER” on page 68.

```
SCF
Sidechain Filter filters the signal from the sidechain source before the compressor analyzes it. The
parameter ranges from a low-pass filter to a high-pass filter. Negative parameter values set a low-pass
filter. Positive parameter values set a high-pass filter.
When set as a low-pass filter, the compressor reacts mostly to bass frequencies. Use this setting for a
characteristic pumping compressor sound. When set as a high-pass filter, the compressor reacts less to
bass frequencies. Use this setting to avoid pumping.
```
```
DRY/WET MIX
Dry/Wet Mix sets the mix of the uncompressed signal and the compressor output signal. A setting of
0 results in a completely uncompressed signal. A setting of 127 lets only the compressed signal pass
through. All values in between mix the uncompressed signal with the compressed signal, which is known
as parallel compression.
```

##### 12. FX AND MIXER PARAMETERS

```
The Digitone II compressor
```
### Filter

### (HP/LP)

### Gain

### Reduction

### Input

### (LR)

### Output

```
Control
Signal
```
### Dry/Wet

### Mix

### Make up

### Gain

### Sidechain

### Source

### Gain

### Computer

### Dynamics

### (Atk/Rel)

SRC

### 12.6 INTERNAL MIXER PAGE 1.

##### (MIXER PAGE 2)

The INTERNAL MIXER page 1 is where you can access the TRACK LEVEL parameter of track 1–8 (if they
are audio tracks).

##### VOL

```
Pattern Volume sets the patterns overall audio level. At a parameter setting of 100, you have unity gain.
Below that, the signal is attenuated. Above that, the signal is amplified. This parameter can be used to
balance the volume between different patterns and is saved together with the kit. Use LEVEL/DATA to
adjust the settings.
```
### 12.7 INTERNAL MIXER PAGE

##### (MIXER PAGE 3)

The INTERNAL MIXER page 2 is where you can access the TRACK LEVEL parameter of track 9–16 (if they
are audio tracks).

##### VOL

```
Pattern Volume sets the patterns overall audio level. At a parameter setting of 100, you have unity gain.
Below that, the signal is attenuated. Above that, the signal is amplified. This parameter can be used to
balance the volume between different patterns and is saved together with the kit. Use LEVEL/DATA to
adjust the settings.
```

##### 12. FX AND MIXER PARAMETERS

### 12.8 FX MIXER

##### (MIXER PAGE 4)

```
On the FX MIXER page you can set the levels of the send effects and master overdrive.
```
##### VOL

```
Pattern Volume sets the patterns overall audio level. At a parameter setting of 100, you have unity gain.
Below that, the signal is attenuated. Above that, the signal is amplified. This parameter can be used to
balance the volume between different patterns and is saved together with the kit. Use LEVEL/DATA to
adjust the settings.
```
```
DEL
Delay Mix Volume Sets the master volume of the delay.
```
```
REV
Reverb Mix Volume sets the master volume of the reverb.
```
```
CHR
Chorus Mix Volume sets the master volume of the chorus.
```
```
MOVD
Master Overdrive sets overall amount of overdrive for the audio tracks in the kit. Use LEVEL/DATA to
adjust the settings.
```
### 12.9 EXTERNAL MIXER

##### (MIXER PAGE 5)

```
The EXTERNAL MIXER page contains a number of parameters related to when you use the IN L/R inputs
to process incoming audio.
```
##### IN LR

```
In Level sets the level of the audio from the IN L/R audio input.
```
```
DUAL
Dual Mono sets if the inputs are processed as a single stereo input or as two separate mono inputs.
Setting this parameter to ON changes the rest of the parameters from controlling both inputs to sep-
arate controls for channel L and R split over two mixer pages The BAL parameters changes to a PAN
parameter.
```
```
BAL
Balance sets the balance of the audio from the INPUT L and INPUT R inputs in the stereo field.
```

##### 12. FX AND MIXER PARAMETERS

##### VOL

Pattern Volume sets the patterns overall audio level. At a parameter setting of 100, you have unity gain.
Below that, the signal is attenuated. Above that, the signal is amplified. This parameter can be used to
balance the volume between different patterns and is saved together with the kit. Use LEVEL/DATA to
adjust the settings..

DEL

Delay send sets the amount of sound from the IN L/R audio input that is sent through to the delay effect.
For more information, please see “12.2 DELAY” on page 63.

REV

Rev send sets the amount of sound from the IN L/R audio input that is sent through to the reverb effect.
For more information, please see “12.3 REVERB” on page 64.

CHO

Chorus send sets the amount of sound from the IN L/R audio input that is sent through to the chorus
effect. For more information, please see “12.4 CHORUS” on page 65.

```
If you set DUAL to ON, the first EXTERNAL MIXER page will control the audio from INPUT
L. You also get an additional EXTERNAL MIXER page (6) that control the audio from IN-
PUT R.
```
```
After you load/reload a project, or after pressing [STOP] + [STOP], you must trig or play
an audio track to activate the effects for the External in mixer.
```

## 13. SETTINGS MENU

13. SETTINGS MENU

```
The SETTINGS menu offers settings that affect Digitone II and can also be used to manage Projects.
Press [SETTINGS] to access the SETTINGS menu. Scroll the list by using [UP]/[DOWN] or the LEVEL/
DATA knob. Open a highlighted menu by pressing [YES].
```
### 13.1 PROJECT

#### 13.1.1 LOAD PROJECT

```
Load Project opens a project selection screen where you can choose a project to load. If you wish to
create a new project, select CREATE NEW at the very bottom of the list. The new project will be a blank
slate.
```
```
If you load a new project it will replace the active project. Be sure to save your active
project before you load another project.
```
#### 13.1.2 SAVE PROJECT AS

```
Save Project opens a project selection screen where you choose a slot to save the active project to.
You can also press [FUNC] + [SETTINGS] to save and overwrite the current project.
```
#### 13.1.3 MANAGE PROJECTS

```
Manage Projects launches the PROJECT MANAGER menu. Selecting a project in this menu and pressing
the [RIGHT] arrow key will bring up a list of commands.
```
- CLEAR Resets the project slot to a clean state.
- DELETE Removes the project from the slot.
- RENAME Opens a NAMING screen where you can rename the project file.
- LOAD FROM Loads the selected project. This will replace the active project!
- SAVE TO Saves the active project to the selected slot.
- TOGGLE Toggles write protection on or off. Write protected projects cannot be overwritten, re-
named or erased. A lock symbols in front of the project name shows that the project is write protected.
- INIT NEW Initializes an empty file slot with a clean project. This option is only available for empty
project slots.
- PURGE ALL Purges (removes) all presets from the pool that are not used in any pattern in the
project from the RAM memory. The presets are not removed from the +Drive library. This option is only
available when you select the currently loaded project.


##### 13. SETTINGS MENU

### 13.2 SONG

In this menu, you can perform tasks related to songs. For more information, please see “10.13 SONG MODE”
on page 51.

#### 13.2.1 RENAME

```
Opens the NAMING screen where you can rename the active song.
```
#### 13.2.2 CLEAR

```
Clears the active song.
```
#### 13.2.3 LOAD

```
Opens a screen where you can select and load songs
```
#### 13.2.4 SAVE TO PROJ TABLE OF CONTENTS

```
Saves the active song to the project.
```
### 13.3 PATTERN MENU

Use the PATTERN menu to perform pattern management. Use the [UP] and [DOWN] arrow keys to move
between the options. Press [YES] to confirm your selection. Press [NO] to exit the menu.

#### 13.3.1 RENAME

```
Opens the NAMING screen where you can rename the active pattern.
```
#### 13.3.2 CLEAR

- WHOLE PATTERN Clears the active pattern’s sequencer data and kit. A prompt appears when you
select this option. Press [YES] to clear, or [NO] to cancel the operation. Note that no pattern informa-
tion will be permanently lost until the pattern is saved to the same pattern slot that it was loaded from.
- KIT DATA Clears the active pattern’s kit. All sequencer data will remain unchanged. A prompt ap-
pears when you select this option. Press [YES] to clear, or [NO] to cancel the operation. Note that no
kit information will be permanently lost until the pattern is saved to the same pattern slot that it was
loaded from.
- SEQUENCE DATA Clears the active pattern’s sequence data. All presets will remain unchanged. A
prompt appears when you select this option. Press [YES] to clear, or [NO] to cancel the operation.
Note that no sequencer information are permanently lost until the pattern is saved to the same pattern
slot it was loaded from.

#### 13.3.3 SAVE TO PROJ

```
You must have saved the project at least once before you can save pattern information.
```
- WHOLE PATTERN Saves the active pattern’s sequence data and kit to the +Drive. A prompt appears
when you select this option. Press [YES] to save, or [NO] to cancel the operation.
- KIT DATA Saves the active pattern’s kit to the +Drive. A prompt appears when you select this option.
Press [YES] to save, or [NO] to cancel the operation.


##### 13. SETTINGS MENU

- SEQUENCE DATA Saves the active pattern’s sequence data to the +Drive. A prompt appears when
you select this option. Press [YES] to save, or [NO] to cancel the operation.

#### 13.3.4 RELOAD FROM PROJ

```
You must have saved the pattern at least once before you can reload pattern information.
```
- WHOLE PATTERN Reloads the active pattern’s sequence data and kit from the +Drive. A prompt will
appear when you select this option. Press [YES] to reload, or [NO] to cancel the operation.
- KIT DATA Reloads the active pattern’s kit from the +Drive. A prompt appears when you select this
option. Press [YES] to reload, or [NO] to cancel the operation.
- SEQUENCE DATA Reloads the active pattern’s all sequence data from the +Drive. A prompt appears
when you select this option. Press [YES] to reload, or [NO] to cancel the operation.

### 13.4 MIDI CONFIG

```
In this menu, various sub-menus dealing with the MIDI functionality of Digitone II are found.
```
#### 13.4.1 SYNC

```
Controls how Digitone II receives and sends MIDI clock and transport commands. Change settings by
using the [LEFT]/[RIGHT] arrow keys or the [YES] key.
```
```
CLOCK RECEIVE sets whether or not Digitone II responds to MIDI clock sent from external devices.
CLOCK SEND sets whether or not Digitone II transmits MIDI clock.
TRANSPORT RECEIVE sets whether or not Digitone II responds to MIDI transport messages sent
from external devices.
TRANSPORT SEND sets whether or not Digitone II transmits MIDI transport messages.
PRG CH RECEIVE will, when active, make Digitone II respond to incoming program change messages,
which is useful when wanting to select patterns externally. The MIDI channel that will listen to incom-
ing program change messages is set in the MIDI CHANNELS menu. For more information, please see
“13.4.3 CHANNELS” on page 74.
PRG CH SEND will, when active, send program change messages when patterns are changed. The
MIDI channel that will send program change messages is set in the MIDI CHANNELS menu. For more
information, please see “13.4.3 CHANNELS” on page 74.
```
#### 13.4.2 PORT CONFIG

```
Here you find the MIDI port related settings. Change settings by using the [LEFT]/[RIGHT] arrow keys.
```

##### 13. SETTINGS MENU

TURBO SPEED press [YES] to start the turbo speed negotiation. Speed is chosen automatically. Please
note that you must use a MIDI interface that supports the Turbo-MIDI protocol.

OUT PORT FUNC selects what type of signal the MIDI OUT port will send.

- MIDI makes it possible for the port to send out MIDI data.
- DIN 24 will make the port send DIN 24 sync pulses. No MIDI data is transferred over the port when
this option is selected.
- DIN 48 will make the port send DIN 48 sync pulses. No MIDI data is transferred over the port when
this option is selected.

THRU PORT FUNC selects what type of signal the MIDI THRU port will send. The settings are the same
as for OUT PORT FUNCTIONALITY.

INPUT FROM selects the source Digitone II will receive MIDI data from.

- DISABLED will make Digitone II disregard any incoming MIDI data.
- MIDI will make Digitone II listen only to MIDI data sent to the MIDI IN port.
- USB will make Digitone II listen only to MIDI data sent to the USB port.
- MIDI+USB will make Digitone II listen to MIDI data sent to both the MIDI IN and USB ports.

OUTPUT TO selects the destination to which Digitone II will send MIDI data.

- DISABLED will stop Digitone II from sending out any MIDI data.
- MIDI will make Digitone II send MIDI data to the MIDI OUT port only.
- USB will make Digitone II send MIDI data to the USB port only.
- MIDI+USB will make Digitone II send MIDI data to both the MIDI OUT and USB ports.

```
If MIDI+USB is selected in the OUTPUT TO settings, MIDI data will limit the USB speed.
When sending large chunks of data, make sure you only use the USB setting.
```
OUTPUT CH selects whether the DATA ENTRY knobs will send data on the auto channel or the track
channel.

PARAM OUTPUT selects what type of MIDI messages the DATA ENTRY knobs will send. For information
about which CC/NRPN parameters that will be sent, please see “APPENDIX C: MIDI” on page 111.

- CC will make the knobs send out CC MIDI messages.
- NRPN will make the knobs send out NRPN MIDI messages.

ENCODER DEST controls whether the DATA ENTRY and LEVEL/DATA knobs sends MIDI data or not.
When set to INT, the knobs only affects the Digitone II and no MIDI data is sent. When set to INT + EXT,
the knobs affects the Digitone II and also sends MIDI data to external devices.

TRIG KEY DEST controls whether the [TRIG] keys sends MIDI data or not. When set to INT, the [TRIG]
keys only affects the Digitone II and no MIDI data is sent. When set to INT + EXT, the [TRIG] keys affects
the Digitone II, and also sends MIDI data to external devices. When set to EXT, the [TRIG] keys do not
affect the Digitone II but MIDI data is sent externally.

MUTE DEST controls whether activating/deactivating mutes will send MIDI data or not. When set to INT,
mute only affects the Digitone II and no MIDI data is sent. When set to INT + EXT, mute affects the Dig-
itone II and also send MIDI data to external devices. When set to EXT, mute sends MIDI data externally,
but do not affect the Digitone II.

RECEIVE NOTES will, when active, make it possible to play Digitone II using an external MIDI keyboard.

RECEIVE CC/NRPN will, when active make it possible to control Digitone II’s parameters from an exter-
nal MIDI device sending CC/NRPN data.


##### 13. SETTINGS MENU

#### 13.4.3 CHANNELS

```
This menu handles the MIDI channel configuration.
```
```
TRACK 1–16 selects the dedicated MIDI channel that is used to receive or send (by turning the knobs)
parameter data to or from a specific audio track. If configured as OFF, parameter data are neither
received nor sent via MIDI.
```
```
The sequencer data is always sent on the MIDI channel specified by the CHAN
parameter on the SYN PARAMETER page.
```
```
FX CONTROL CH selects the dedicated MIDI channel that is associated with the parameters on the
DELAY, REVERB, CHORUS, and COMPRESSOR parameter pages together with the master overdrive,
both for input and output. If configured as OFF, parameter data are neither sent nor received via MIDI.
AUTO CHANNEL selects the MIDI channel that will give access to the currently active track. If an ex-
ternal MIDI keyboard connected to Digitone II sends MIDI data on this channel, the keyboard will con-
trol the active track. This is useful when for example quickly changing between the active audio tracks
to play different presets. The Digitone II also uses the AUTO channel to record to the MIDI tracks from
external MIDI controllers.
PROGRAM CHG IN CH selects the MIDI channel that will listen for incoming program change mes-
sages. An AUTO setting will use the AUTO channel. Enable Digitone II to respond to program change
messages in the MIDI SYNC menu. For more information, please see “13.4.1 SYNC” on page 72.
PROGRAM CHG OUT CH selects the MIDI channel that will send program change messages when
changing patterns. An AUTO setting will use the AUTO channel. Enable Digitone II to send program
change messages in the MIDI SYNC. For more information, please see “13.4.1 SYNC” on page 72.
```
### 13.5 SYSEX DUMP

```
In the SYSEX DUMP menu, project, pattern, and preset data can be sent and received via the MIDI OUT
port or the USB port of the Digitone II. Select a menu option using [UP]/[DOWN] or the TRACK LEVEL
knob. Press [YES] to open the highlighted menu selection.
```
```
When receiving or sending SysEx data, the MIDI ports or the USB port of the Digitone II should be connect-
ed to the external sending/receiving device.
```
#### 13.5.1 SYSEX SEND

```
Here projects, patterns, and presets can be sent to an external device.
```

##### 13. SETTINGS MENU

```
The column to the left selects what will be backed up. Select the column using the [LEFT] arrow key.
Use the [UP]/[DOWN] keys or the LEVEL/DATA knob to navigate in the column. The SysEx data send
alternatives located in the column to the right will change depending on the selection made in the left
column. Press the [RIGHT] arrow key to access this column. Use the [UP]/[DOWN] keys or the LEVEL/
DATA knob to select what will be sent. Press [YES] to initiate the SysEx send procedure.
PROJECT will send the active project (settings, patterns, presets in the pool).
PATTERN will send the selected pattern.
```
- Backing up your data regularly is important!
- Before initiating a SysEx send, first, make sure the receiving device is listening for data
    to be sent.

#### 13.5.2 SYSEX RECEIVE

```
Here projects, patterns, and presets can be received from an external device. Digitone II is continuously
listening for SysEx data so you can at any time send backed up projects or patterns to the device.
```
```
The column to the left selects what will be received. Select the column using the [LEFT] arrow key. Use
the [UP]/[DOWN] keys or the LEVEL/DATA knob to navigate in the column. The SysEx data receive
alternatives located in the column to the right will change depending on the selection made in the left
column. Press the [RIGHT] arrow key to access this column. Use the [UP]/[DOWN] keys or the LEVEL/
DATA knob to select what will be received. Press [YES] to initiate the SysEx receive procedure. The
Digitone II starts listening to incoming data. Press [NO] to stop listening.
PATTERN will store a received pattern to the selected pattern slot.
PRESETS will store a received preset to the selected slot of the +Drive library. The option ANY-
WHERE will place the preset in the first free slot available. To the right of the bank indication, the
amount of free slots can be seen.
```
### 13.6 AUDIO ROUTING

Here you find number of audio routing options that affects the Digitone II on a global level.

#### 13.6.1 TO MAIN

```
Use this option to customize which of the 16 tracks, 3 effects (Delay, Reverb, Chorus), and inputs that
will send audio to the MAIN OUT outputs. Use [UP]/[DOWN] to switch between the setup pages. Use
the [TRIG] keys to activate/deactivate the tracks, effects, and inputs that can send to main. Green keys
signal send to main. Unlit keys signal do not send to main.
```
#### 13.6.2 TO SEND FX

```
Use this option to customize which of the 16 tracks, 2 effects (Delay, Chorus), and inputs that will send
audio to the effects. Use [UP]/[DOWN] to switch between the setup pages. Use the [TRIG] keys to ac-
tivate/deactivate the tracks, effects, and inputs that can send to FX. Green keys signal send to FX. Unlit
keys signal do not send to FX.
```

##### 13. SETTINGS MENU

#### 13.6.3 USB IN

```
Sets where the incoming audio from the class compliant device is routed to in the Digitone II’s signal
path. This parameter is only available when USB CONFIG is set to USB AUDIO/MIDI. For more informa-
tion, please see “13.8.1 USB CONFIG” on page 77.
MAIN The incoming audio is routed to the Digitone II’s main outputs.
OFF No sound is routed from USB to the Digitone II.
```
#### 13.6.4 USB OUT

```
Sets from where in the Digitone II’s signal path, the outgoing audio is routed to the class compliant
device. This parameter is only available when USB CONFIG is set to USB AUDIO/MIDI. For more informa-
tion, please see “13.8.1 USB CONFIG” on page 77.
MAIN The outgoing audio is routed from the Digitone II’s Main out at the end of the signal path.
L:T1–16/R:T1–16 The outgoing audio is routed from the selected track(s) Press a [TRIG] key twice to
select a single track as a stereo source. The selected track’s [TRIG] key lights up white.
Press first one [TRIG] key and then another to select two separate tracks as sources. The selected
tracks’ [TRIG] keys lights up blue for left channel and red for right channel. The audio from each track
is then summed to mono, routed out and sent separately on left and right channel.
EXT The outgoing audio is routed straight from the Digitone II’s audio inputs IN L/R to the class com-
pliant device.
OFF No audio is sent to the class compliant device.
```
#### 13.6.5 INT TO MAIN

```
Sets if Digitone II sends internal audio to the MAIN OUT and HEADPHONES OUT or not when used with
a class compliant audio device.
OFF No sound is sent to main out.
ON Sound is always sent to main out.
```
#### 13.6.6 USB TO MAIN [dB]

```
Sets the amount of amplification of the sound that is streamed over USB to the Digitone II main out when
used with a class compliant audio device. (0 dB–+18 dB)
```
#### 13.6.7 PRE/POST FADER

```
Sets if audio over USB should be pre or post the track level setting.
```
```
Audio from the TRACK OUTPUTS is always without any effects.
```
### 13.7 PERSONALIZE

```
Here you can customize a number of settings to fit you personal preferences.
```
#### 13.7.1 LED INTENSITY

```
Sets the brightness of the key LEDs and screen. Use the [LEFT]/[RIGHT] keys to change the setting
(LOW, MID, MAX).
```
#### 13.7.2 LED BACKLIGHT

```
Switches the key LEDs backlight on/off.
```

##### 13. SETTINGS MENU

```
Press and hold [SETTINGS] for a second and then press [TRIG 1–3] to set the intensity of
the key LEDs and screen. Press and hold [SETTINGS] for a second and then press [TRIG
9] to toggle the key LEDs backlight on/off.
```
#### 13.7.3 REMEMBER SUBPAGE

```
Will, when selected, remember which PARAMETER page’s subpage you last used and access this page
again the next time you press the same [PARAMETER] key. Use the [YES] key to toggle this setting.
(ON, OFF).
```
#### 13.7.4 U/D KEY MODE

```
Sets the functionality of the [UP]/[DOWN] keys. When set to KB OCT and you are in KEYBOARD mode,
the keys shift keyboard octave up/down. When you are not in KEYBOARD mode, then keys navigates
between parameter group pages. When set to NAV, the keys only navigate between parameter pages.
```
#### 13.7.5 PAGE AUTOCOPY

```
Will, when set to on, automatically copy already placed trigs when extending the length of a pattern. If
a pattern consists of for example two pages and the pattern length is increased to four pages, the two
additional pattern pages are then copies of the first two pattern pages. For more information, please see
“10.11 PAGE SETUP MENU” on page 46.
```
#### 13.7.6 NOTE PARAM

```
Sets the interaction behavior for the NOTE parameter on the TRIG PARAMETERS page.
CHRO: Turning DATA ENTRY knob A changes the parameter in chromatic steps. Pressing and turning
DATA ENTRY knob A changes the parameter in steps that follows the set scale.
SCALE: Turning DATA ENTRY knob A changes the parameter in steps that follows the set scale. Press-
ing and turning DATA ENTRY knob A changes the parameter in chromatic steps.
For more information, please see “8.5.2 KEYBOARD SETUP MENU” on page 24 and “11.2 TRIG PAGE
1” on page 55.
```
### 13.8 SYSTEM

The System menu contains a number of system related settings for the Digitone II.

#### 13.8.1 USB CONFIG

```
Here you find settings related to USB audio and MIDI. Selecting one mode disables the other two modes.
```
```
USB MIDI select this option if you wish to send and receive MIDI over USB.
USB AUDIO/MIDI sets the Digitone II to send and receive audio and MIDI over USB. Select this option
if you want to use the Digitone II together with a class compliant USB audio host.
```

##### 13. SETTINGS MENU

#### 13.8.2 OS UPGRADE

```
Use this menu option when you want to upgrade the Digitone II OS. To send the OS file, use our free
Elektron Transfer software. Elektron Transfer can be downloaded from the Elektron website.
The device sending the OS file must be connected to the USB port of Digitone II.
Please note that the Digitone II will not appear as an icon on your computer desktop.
```
1. Download the Digitone II OS file from the Elektron website.
2. Connect the Elektron device to the computer via USB.
3. Open the Transfer application on your computer.
4. On the Transfer CONNECTIONS page, set the MIDI IN and MIDI OUT ports to your Elektron device.
5. On the Transfer DROP page, drag and drop the OS file. The OS file is then automatically trans-
    ferred to the Elektron device and the OS update initiates. A progress bar is visible on device screen
    when receiving the OS.
6. On your device. Press [YES] to confirm the OS update.
When the update is done, the Digitone II will reboot.

#### 13.8.3 FORMAT +DRIVE

```
You have the possibility to erase all content of the +Drive. Once you have made your choice using the
[LEFT]/[RIGHT] keys and confirmed by pressing [YES], a prompt will appear asking if you want to exe-
cute the formatting procedure. Press [YES] to proceed with the formatting.
```
```
PROJ+PST+KIT Erases all projects, presets, and kits.
```
#### 13.8.4 MASTER TUNE

```
Sets the master tune for the whole device. Use [LEFT]/[RIGHT] to edit the value.
```

## 14. STARTUP MENU

14. STARTUP MENU

To access this menu, hold down the [FUNC] key while powering up the Digitone II. From here you can per-
form a variety of tasks. To choose the different alternatives, press the corresponding [TRIG] key.

### 14.1 TEST MODE

To enter this mode, press the [TRIG 1] key.

```
For testing purposes, a short sound is heard through all outputs of the unit.
```
If you have any trouble with your Digitone II and suspect it may be due to a hardware problem, perform this
self-test. The [UP] and [DOWN] keys can be used to scroll through the test log. A fully functional device
should not report any errors. If it does report an error, please contact Elektron support or the retailer where
you bought your Digitone II from.

### 14.2 EMPTY RESET

To perform this operation, press the [TRIG 2] key. All patterns and presets will be erased. The data on the
+Drive remains intact.

### 14.3 FACTORY RESET

When performing a factory reset on the Digitone II, it will overwrite and re-initialize the active RAM project
(including all pattern and global data). The +Drive project slot 1 will be overwritten and re-initialized with fac-
tory preset patterns, presets and settings. Preset bank A and B will be overwritten with the factory presets.

If you wish to keep the active project, remember to save it to a +Drive project slot higher than 1 before you
perform a factory reset. To perform a factory reset, press the [TRIG 3] key.

### 14.4 OS UPGRADE

Use this menu option if you for some reason cant upgrade the Digitone II OS using the standard procedure
in the SYSTEM menu. To send the OS file, use our free Elektron Transfer software. The Elektron Transfer
can be downloaded from the Elektron website.

1. Download the Digitone II OS file from the Elektron website.
2. Connect the Digitone II’s MIDI IN port to the MIDI OUT port of the computer’s MIDI interface.
3. Hold down the [FUNC] key while powering on Digitone II. This takes you to the STARTUP menu.
4. Press the [TRIG 4] key to enter OS UPGRADE mode.
5. Open the Transfer application on your computer. On the Transfer CONNECTION page, click “go to the
    SYSEX TRANSFER page”.
6. On the SYSEX TRANSFER page, click “OS Upgrade via device startup menu”, and then follow the on-
    screen instructions.

When the update is done, the Digitone II will reboot.

```
USB MIDI transfer is not possible when upgrading the OS from the STARTUP menu.
```
### 14.5 EXIT

Press the [TRIG 5] key to exit the STARTUP menu.


## 15. SETUP EXAMPLES

15. SETUP EXAMPLES

```
The Digitone II likes to play with other machines. Whether it uses its ability to sync and play with legacy
machines or controls other synthesizers: Digitone II gets along with other gear.
```
### 15.1 DIGITONE II WITH A MONOPHONIC BASS MACHINE

```
The DIN sync capabilities of the Digitone II allows you to use gear from yesteryear.
In this example, a legacy monophonic bass machine is used alongside the Digitone II. The Digitone II can
stop, start and control the tempo of the bass machine.
```
1. Prepare a bassline pattern on the bass machine.
2. Connect the output of the bass machine to the mixer using a Mono jack 6.3 mm male cable.
3. Connect the Digitone II audio outputs to the mixer using 2 x Mono or Stereo jack 6.3 mm male cables.
4. Use a DIN connector cable to connect the MIDI OUT of the Digitone II to the SYNC IN of the bass
    machine.
5. On the Digitone II, press [SETTINGS], and then navigate to MIDI CONFIG > PORT CONFIG and set
    OUT PORT CONFIG to DIN24.
6. Press [PLAY] on the Digitone II.

### 15.3 CONTROLLING A SYNTHESIZER USING THE MIDI TRACKS


##### 15. SETUP EXAMPLES

Digitone II has extensive capabilities to use its sequencer’s MIDI tracks to control other MIDI-equipped
synthesizers.

1. Use a standard MIDI cable to connect the Digitone II MIDI OUT jack with the synthesizer’s MIDI IN
    jack.
2. On the Digitone II, press [SETTINGS], and then navigate to MIDI CONFIG > PORT CONFIG and set
    OUT PORT FUNC to MIDI.
3. In the same menu set OUTPUT to MIDI.
4. On the main screen, press [TRK] + [TRIG 1-16] to select a track that uses a MIDI SYN machine.
5. Press [SYN] and use the CHAN parameter to select a MIDI channel that the track will output its data to.
6. Make sure to set your synthesizer to receive MIDI input in a way that corresponds to the settings you
    made in the Digitone II.

You are now ready to use the Digitone II sequencer to control your synthesizer. For more information about
using the Digitone II sequencer, please see “10. THE SEQUENCER” on page 39.


## 16. KEY COMBINATIONS

16. KEY COMBINATIONS

```
Use the key combinations below to quickly perform certain tasks.
```
### COPY/PASTE/CLEAR

##### TRACK SEQUENCE (ALL TRIGS ON THE TRACK)

```
When in GRID RECORDING mode.
[FUNC] + [RECORD] to copy the active track’s sequence.
[FUNC] + [STOP] to paste the copied track’s sequence to the active track.
[FUNC] + [PLAY] to clear the active track’s sequence.
```
```
TRIG
When in GRID RECORDING mode
[TRIG] + [RECORD] to copy the trig with it’s parameter locks.
[TRIG] + [STOP] to paste the copied trig.
[TRIG] + [PLAY] to clear the trig.
```
```
PATTERN
[FUNC] + [RECORD] to copy the active pattern.
[FUNC] + [STOP] to paste the copied pattern to the active pattern.
[FUNC] + [PLAY] to clear the active pattern.
```
```
PRESET
[TRK] + [RECORD] to copy the selected track’s preset.
[TRK] + [STOP] to paste the copied preset to the selected track.
[TRK] + [PLAY] to clear the selected track’s preset.
```
```
SEQUENCER PAGE
When in GRID RECORDING mode
[PAGE] + [RECORD] to copy the active page.
[PAGE] + [STOP] to paste the copied page to the active page.
[PAGE] + [PLAY] to clear the active page.
```
### TRACK/PATTERN/BANK SELECT

```
[TRK] + [TRIG 1-16] keys to select a track.
[FUNC] + [PTN] + [TRIG 1-16] to choose a pattern bank
[PTN] and [LEFT]/[RIGHT] and [TRIG 1-16] keys to select a bank/pattern.
```
### NAMING

```
[FUNC] + [ARROW] (on the NAMING screen) to choose a letter.
[FUNC] + [NO] (on the NAMING screen) to erase a letter.
[FUNC] + [YES] (on the NAMING screen) to add a blank space.
```
### SAVING AND RELOADING

##### PATTERN

```
[FUNC] + [NO] to reload the active pattern.
[FUNC] + [YES] to temporarily save the active pattern.
```
```
PRESET
When in GRID RECORDING mode.
[TRIG] + [PRESET/KIT] save the sound of a trig as a preset, taking all parameter locks in consideration.
[TRIG] + [YES] previews the trig.
```

##### 16. KEY COMBINATIONS

[FUNC] + [SETTINGS] saves the active project.

[FUNC] + [YES] temporary saves the active pattern.

[FUNC] + [NO] temporary reloads the active pattern.

[PARAMETER] key + [NO] reloads all parameters on that parameter page from its last saved state

[PARAMETER] key + [YES] randomizes parameters on that parameter page

[TRK] + [TRIG1–16] + [NO] reloads the preset from its last saved state

### MENU ACCESS

[FUNC] + [SONG] opens the SONG EDIT menu.

[FUNC] + [TRIG PARAMETERS] opens the QUANTIZE menu.

[FUNC] + [SYN] opens the MACHINE menu.

[FUNC] + [FLTR] opens the SETUP menu.

[FUNC] + [AMP] opens the SEQUENCER menu.

[FUNC] + [FX] opens the SEND FX pages.

[FUNC] + [MOD] opens the MIXER pages.

[FUNC] + [PAGE] opens the pattern/track PAGE SETUP menu.

[FUNC] + [KEYBOARD] opens the keyboard setup menu.

### SEQUENCER SETTINGS

[FUNC] + [TEMPO] to tap tempo.

[LEFT/RIGHT] to nudge tempo (when sequencer is playing).

[FUNC] + [LEFT/RIGHT] moves all trigs a whole step, left or right (when in GRID RECORDING mode).

[TRIG] key (hold) + [LEFT/RIGHT], opens the MICRO TIMING menu and sets the micro timing for the trig
(when in GRID RECORDING mode).

[FUNC] + [UP]/[DOWN] (In the PAGE SETUP menu) sets the TRACK LENGTH in musical increments from
2/16 to 64/64.

### SEQUENCER RECORDING

[RECORD] + [PLAY] starts LIVE RECORDING.

[RECORD] + [STOP] activates STEP RECORDING.

[RECORD] + double-press [PLAY] activates/deactivates QUANTIZE LIVE RECORDING.

Hold [PAGE] and press [LEFT]/[RIGHT] to change sequencer page, or hold [PAGE] and press one of the
lit [TRIGS] to jump to that page (when in GRID RECORDING mode).

### MUTES

[FUNC] + [TRIG] keys mutes/unmutes one or several tracks.

When in MUTE mode, press and hold [FUNC] + [TRIG] keys to prepare the track to be muted/unmuted.
Releasing [FUNC] will perform the action.

### MODES

[FUNC] + [TRK] enters GLOBAL MUTE mode.

[FUNC] + double-press [TRK] enters PATTERN MUTE mode.

[KEYBOARD] enters/exits KEYBOARD mode.

[FUNC] + [UP]/[DOWN] accesses the TRIG mode menu

[YES] + [PAGE] activates FILL mode for one pattern cycle. Pressing [PAGE] again deactivates FILL mode.

When not in GRID RECORDING mode, press and hold [PAGE] to activate FILL Mode for as long as [PAGE]
is held.

Press and hold [PAGE] + [YES], and then release [PAGE] before you release [YES] to latch FILL mode.
Press [PAGE] again to unlatch FILL mode.


##### 16. KEY COMBINATIONS

### RESET PARAMETERS

```
Press DATA ENTRY knob + [NO] to reset the parameter to the default value.
[PARAMETER] key + [PLAY] to reset all the parameters in the selected parameter page to
default values.
```
### PRESET/TRIG PREVIEW

```
[FUNC] + [YES] (In the PRESET BROWSER/PRESET MANAGER) previews the highlighted preset.
[TRIG] + [YES] (In GRID- and STEP RECORDING mode) previews the selected trig.
```
### SONG MODE

```
[SONG] + [TRIG 1–16] to select song and enter SONG mode.
[FUNC] + [SONG] to open the SONG EDIT screen.
[SONG] + [LEFT] (when in SONG mode) to loop the currently playing row. Press [SONG] + [LEFT] again to
stop looping the row and return to normal song playback.
[SONG] + [UP] (when in SONG mode) to move the song pointer up one row.
[SONG] + [DOWN] (when in SONG mode) to move the song pointer down one row.
Press [SONG] (when in SONG mode) twice to open the SONG EDIT screen.
[FUNC] + [RECORD] (when in the SONG EDIT screen) copies the selected row.
[FUNC] + [STOP] (when in the SONG EDIT screen) pastes a previously copied row to the selected row.
[FUNC] + [PLAY] (when in the SONG EDIT screen) resets the selected row to the pattern’s default BPM,
length, and mute state.
[FUNC] + [DOWN] (when in the SONG EDIT screen) adds a new row.
[FUNC] + [UP] (when in the SONG EDIT screen) deletes the selected row.
```
### TRANSPOSE

```
[FUNC] + [+]/[-] to transpose the current track up/down in semitones.
[TRK] + [+]/[-] to transpose the current track up/down in semitones.
[TRK] + [FUNC] + [+]/[-] to transpose the current track in octaves (+-12 semitones).
[PTN] + [+]/[-] to transpose the pattern up/down in semitones.
[PTN] + [FUNC] + [+]/[-] to transpose the pattern up/down in octaves (+-12 semitones).
When in GRID RECORDING mode [TRIG] + [+]/[-] to transpose the current trig in semitones (destructively).
```
### NOTE EDIT

```
[FUNC] + [LEFT]/[RIGHT] to go to the previous/next sequencer step that contains a note trig.
[LEFT]/[RIGHT] to go the previous/next sequencer step regardless if it contains a trig or not.
[FUNC] + [UP] to delete the highlighted note.
[FUNC] + [DOWN] to add a note below the highlighted note.
[FUNC] + [YES] to preview the selected step.
[FUNC] + [PAGE] to navigate to the next pattern page.
When in GRID RECORDING mode [TRIG] + [NOTE EDIT] to jump to that trig in Note Edit.
```
### TRACK

```
[FUNC] + [VOICE] to toggle the current track’s unison mode on/off.
[FUNC] + [ARP] to toggle the current track’s arpeggiator on/off.
```

## 17 TECHNICAL INFORMATION

17. TECHNICAL INFORMATION

### ELECTRICAL SPECIFICATIONS

Impedance balanced audio outputs
Main outputs level: +18 dBu peak
Output impedance: 440 Ω unbalanced

Headphones output
Headphones out level: +18 dBu peak
Output impedance: 36 Ω

Audio inputs
Input level: +18 dBu peak
Audio input impedance: 21 kΩ

Unit power consumption: 7 W typical

### Recommended power supply: PSU-3c (12 V DC, 2A)

### HARDWARE

```
128 × 64 pixel OLED screen
MIDI In/Out/Thru with DIN Sync out
2 × 1/4” impedance balanced audio out jacks
2 × 1/4” balanced audio in jacks
1 × 1/4” stereo headphone jack
48 kHz, 24-bit D/A and A/D converters
Hi-speed USB 2.0 port
Power inlet: Center positive 5.5 × 2.5 mm barrel jack,
12 V DC, 1 A
```
### PHYSICAL SPECIFICATIONS

```
Sturdy steel casing
Dimensions: W 215 × D 176 × H 63 mm
(8.5” × 6.9” × 2.5”) (including knobs and feet)
Weight: approximately 1.48 kg (3.25 lbs)
100 × 100 mm VESA mounting holes. Use M4 screws
with a max length of 7 mm.
Maximum recommended ambient operating
temperature: +40 ̊C (+104 ̊F)
```

## 18 CREDITS AND CONTACT INFORMATION

18. CREDITS AND CONTACT INFORMATION

### CREDITS

##### ELEKTRON CREW

```
Lennart Ahlstedt
Johannes Algelind
Magnus Almberg
Christian Alsing
Hans Alvarsson
Deva Andar
Nikolaj Andersson
Madeleine Antonsson
Anyere Bendrien
Andreas Brykt
Chloe Corley
Johan Damerau
Oscar Dragén
Magnus Forsell
Jennifer Giöbel
Birgitta Hedström
Mario Adriane Hernandez
Tomas Hjalmarsson
Thomas Jansson
Patrik Johansson
Christian Karlsson
George Kaplan
Åsa Larsson
Erik Liakhovets
Christer Lindström
Joel Lundberg
Johannes Mai
Enrique Martinez
Jimmy Myhrman
Viktor Nilsson
Jean Michel Pepin
Mattias Rickardsson
Patrik Rinvall
Viktor Sandström
Matthias Tellen
David Smallbone Tizard
Che Thomas
Jake Widgeon
Vladislav Zhukov
Erik Ångman
```
##### FACTORY LIBRARY CONTENT

```
Factory Presets
Michael Feiner
Jogging House
Chad Mossholder
Aho Ssan
The Elektron Crew
Factory Patterns
Jeremiah Chiu
Lying Dalai
Ash Farrand aka Hissquiet
Michael Feiner
Jogging House
Gareth Mallinson
The Elektron Crew
```
##### BETA TESTING

```
To our amazing beta testers—you know who you
are; this product is better because of you!
```
### CONTACT INFORMATION

##### ELEKTRON WEBSITE

```
https://www.elektron.se
```
```
OFFICE ADDRESS
Elektron Music Machines MAV AB
Banehagsliden 5
SE-414 51 Gothenburg
Sweden
```

## APPENDIX A: MACHINES

APPENDIX A: MACHINES

A machine is a module within the Digitone II with specific functionality. A machine can be switched out for
another machine in the same category. For example different synthesis engines or filters. Every machine
has a specific set of parameters tailored to give you the most relevant and useful sound-shaping possibili-
ties for that particular machine. For more information, please see “5.3.1 AUDIO TRACKS AND MACHINES”
on page 16

Use the [UP]/[DOWN] keys to select the parameter group's pages. You can also press a [PARAMETER]
key repeatedly to cycle trough the parameter pages in that group. Press and hold a [PARAMETER] key to
see the values for all parameters on that page.

### A.1 ASSIGNING MACHINES TO THE ACTIVE TRACK

1. Press [FUNC] + [SYN] to open the MACHINE menu.
2. Use [LEFT]/[RIGHT] to navigate to the desired machine category.
3. Use [UP]/[DOWN] to select machine, and then press [YES] to assign the selected machine to the
    track.

The rest of this appendix lists the machine-specific parameters on the SYN and FLTR pages. The selected
machine determines the parameters available.

### A.2 SYN MACHINES

The SYN machines are different synthesis engines with unique functionalities. In this section you also find
the MIDI machine that lets you control external, MIDI equipped, gear. For more information, please see
“5.3.2 MIDI TRACKS” on page 17. You can assign any SYN machine to any track. Press [SYN] to access
these parameter pages.

#### A.2.1 FM TONE TABLE OF CONTENTS

```
The Digitone II's FM TONE machine is a four operator Frequency Modulation (FM) synth in the style of
the classic 80s implementations. However, unlike the early FM synths, the Digitone II use its FM engine
more like a complex tone generator than a complete synthesizer voice (although it does have this capa-
bility too). The Digitone II signal path is more similar to a typical subtractive synth than a classic FM voice.
```
```
PAGE 1
The parameters on the page 1 control various aspects of the FM engine. You can find more informa-
tion about the Digitone II FM synthesis in “APPENDIX B: THE FM TONE SYNTHESIS” on page 105.
```
##### ALGO

```
Algorithm selects the structure of how the four operators are connected to each other.
For more information, please see “B.3 ALGORITHMS” on page 106.
```
```
RATIO C
Sets the frequency ratio for operator C. For more information, please see “B.4 FM RATIOS” on page
106.
```

##### APPENDIX A: MACHINES

##### RATIO A

```
Sets the frequency ratio for operator A. For more information, please see “B.4 FM RATIOS” on page
106.
```
```
RATIO B
Sets the frequency ratio for operator B1 and B2. The minimum value for B1 and B2 is .25. As you
turn the encoder, B2 increases until it reaches the max (16). It then starts over from .25 and B1
increases to the next value (0.5). This revolving behavior continues until both operators reach
the maximum value. This parameter behavior is similar to the movement of the hands on a watch.
(0.25–16.0) For more information, please see “B.4 FM RATIOS” on page 106.
```
```
HARM
Harmonics controls waveform of the operators, C, A, and B1. The parameter is bipolar. Negative
parameter values change the harmonics of operator C, while positive parameter values change the
harmonics of operators A and B1. (-26.00–26.00) For more information, please see “B.6 HARMON-
ICS” on page 108.
```
```
DTUN
Detune offsets the ratio of operator A and B2. Up to a parameter value of around 64, the offset is
very slight to achieve subtle movement. Above 64 the operators start to detune more heavily.
```
```
FDBK
Feedback sets the amount of self modulation of the operator that has feedback. This operator is
shown in the algorithm on the screen with a feedback loop in its upper left corner. For more infor-
mation, please see “B.2 OPERATORS” on page 105.
```
```
MIX
Each algorithm has two carrier outputs (X and Y) that come from two different operators depend-
ing on what algorithm you chose. Use the MIX parameter to mix between these outputs so that
you can cross-fade between two separate timbres. (-64–63). For more information, please see For
more information, please see “B.3 ALGORITHMS” on page 106.
```
```
PAGE 2
The parameters on the page 2 control various aspects of the FM synthesis, mainly the amount of
frequency modulation together with the operator envelopes.
The FM engine has two operator envelopes. One is for operator group A, and one is for group B (B1
and B2). The envelopes are essentially expanded AD (Attack Decay) envelopes, but with an added
adjustable end level (the amplitude level the sound reaches at the end of the decay stage). For more
information, please see “B.5 OPERATOR ENVELOPES” on page 107.
```
##### AT K (A)

```
Attack Time sets the length of the attack phase of the modulation envelope for operator A.
```
```
DEC (A)
Decay Time sets the length of the decay phase of the modulation envelope for operator A.
```
```
END (A)
End Level sets the end level of the modulation envelope for operator A.
```
```
LEV (A)
Level sets the modulation amount from operator A.
```
```
ATK (B)
Attack Time sets the length of the attack phase of the modulation envelope for operator group B
(B1 and B2).
```

##### APPENDIX A: MACHINES

##### DEC (B)

```
Decay Time sets the length of the decay phase of the modulation envelope for operator group B
(B1 and B2).
```
```
END (B)
End Level sets the end level of the modulation envelope for operator group B (B1 and B2).
```
```
LEV (B)
Level sets the modulation amount from operator group B (B1 and B2).
The LEVEL parameter for B is macro mapped to both operator B1 and B2 and control their modula-
tion amount as per this graph:
```
```
B1
B2
```
```
Level
Parameter value 43 64 85
```
##### 0

##### 127

```
If you wish to use frequency modulation, it is important that you turn the LEV parame-
ters up, since they set the amount of frequency modulation in the FM engine.
```
##### PAGE 3

The parameters on the page 3 control various aspects of the FM synthesis, mainly the amount of
frequency modulation together with the operator envelopes and their behavior.

##### ADEL

```
Envelope delay sets the time before attack phase of the modulation envelope for operator A starts.
```
```
Delay Attack Decay End level
```
##### ATRG

```
Envelope trig sets the trig behavior of the operator envelopes. The envelopes can be either trig-
gered or gated - making it either an ADE (Attack Decay End) or an ASDE (Attack Sustain Decay
End) envelope. The sustain phase does not have an adjustable envelope level. It is instead the LEV
parameter that sets the sustain level. The note length defines the length of the sustain phase.
```

##### APPENDIX A: MACHINES

```
Trigged (ATRG/BTRG ON)
```
### Attack Decay End level

```
Note on
```
```
Gated (ATRG/BTRG OFF)
```
### Attack Sustain Decay End level

```
Note on Note off
```
##### ARST

```
Envelope reset sets if the envelopes should reset or not when they are retrigged.
Reset on (ARST/BRST ON)
```
```
Trig Trig
```
### Reset on

### Reset off

```
Trig Trig
```
```
Reset off (ARST/BRST OFF)
```
```
Trig Trig
```
### Reset on

### Reset off

```
Trig Trig
```
##### PHRT

```
Phase reset sets if the operators phase are reset to start at 0 or not when they are trigged.
OFF do not reset any operators
ALL resets all operators
C resets operator C
A+B resets operator A, B1, and B2
A+B2 resets operator A and B2
```

##### APPENDIX A: MACHINES

##### BDEL

```
Same as ADEL but for operator group B (B1 and B2).
```
```
BTRG
Same as ATRG but for operator group B (B1 and B2).
```
```
BRST
Same as ARST but for operator group B (B1 and B2).
```
```
PAGE 4
The parameters on the page 4 controls the operators ratio offsets and the key scaling.
```
##### RATIO OFFSET C, A, B1, B2

```
Adds offsets to the ratios of each of the four operators.
```
```
KEY TRACK A
Key track sets the level of how much the modulation output from operator A is affected by what
note you play on the keyboard. If you set key track to 0, the modulation level is the same for all
keys. A higher setting decreases the modulation level more and more the higher you play the key-
board. So, a lower level of modulation decreases the complexity of the tone in the higher frequen-
cies, which is a typical behavior in many acoustic instruments.
```
```
KEY TRACK B1
Same as KEY TRACK A, but for operator B1.
```
```
KEY TRACK B2
Same as KEY TRACK A, but for operator B2.
```
#### A.2.2 FM DRUM

The Digitone II’s FM DRUM machine is an FM engine specially tailored to produce drums and other per-
cussive sounds, although it also works well for melodic sounds. Below, you see the FM DRUM machine’s
voice architecture.

##### FM

##### OPERATORS

##### FM

##### ENVELOPES

##### BODY

##### ENVELOPE

##### WAVE

##### FOLDER

```
LEVEL
```
##### NOISE LEVEL

##### TRANSIENT LEVEL

##### B/W

##### FILTER

##### NOISE

##### ENVELOPE

##### +

##### +

```
AMP
```
```
AMP
```
```
OPERATOR B IN ALGORITHM 5–7 TO FILTER
```

##### APPENDIX A: MACHINES

##### PAGE 1

##### TUNE

```
Tune sets the pitch of the oscillator.
```
```
STIM
Sweep Time sets the pitch sweep time. Lower values result in shorter sweep.
```
```
SDEP
Sweep Depth sets the depth of the pitch sweep.
```
```
ALGO
Algorithm selects the structure of how the four operators are connected to each other.
```
```
O P.C
Operator C Wave sets the waveform for Operator C.
```
```
OP.AB
Operator AB Wave sets the waveform for Operators A and B.
```
```
FDBK
Feedback sets the amount of self modulation of the operator that has feedback. This operator is
shown in the algorithm on the screen with a feedback loop in its upper right corner.
```
```
FOLD
Fold sets the amount of wavefolding of the body part of the sound. (The noise and transient parts
of the sound are excluded.) The wavefolding increases the complexity of the wave and creates a
more overtone rich sound.
```
```
PAGE 2
```
##### DEC

```
Decay A sets the length of the decay phase of the modulation envelope for operator A.
```
```
END
End A sets the end level of the modulation envelope for operator A.
```
```
RATIO
Ratio A sets the frequency ratio for operator A.
```
```
MOD
Mod A sets the modulation amount from operator A.
```
```
DEC
Decay B sets the length of the decay phase of the modulation envelope for operator B.
```
```
END
End B sets the end level of the modulation envelope for operator B.
```

##### APPENDIX A: MACHINES

##### RATIO

```
Ratio B sets the frequency ratio for operator B.
```
```
MOD
Mod B sets the modulation amount from operator B.
```
PAGE 3

##### HOLD

```
Body Hold sets the time before the decay phase starts for the body part of the sound.
```
```
DEC
Body Decay sets the length of the decay phase for the body part of the sound. The last value is
infinite.
```
```
PH.C
OP C Phase sets the reset of the phase for operator C. A parameter value less than 90 means that
phase reset is on, and the value represents the phase start position in degrees. At 90, the operator
resets at the peak of the waveform. When set to OFF (value 91), operator C’s phase is not reset.
```
```
LEV
Body Level sets the level of the body part of the sound.
```
```
NRST
Noise Reset will (when switched on) reset the noise to the same random seed on every note on.
This can be useful if you want the noise part to always sound the same, like if it was a sample.
```
```
NRM
Noise Ring Mod will (when switched on) use Operator C as ring modulator for the noise.
```
PAGE 4

##### NHLD

```
Noise Hold sets the time before the decay phase starts for the noise part of the sound.
```
```
NDEC
Noise Decay sets the length of the decay phase for the noise part of the sound. The last value is
infinite.
```
```
TRAN
Drum Transient selects between different transients.
```
```
TLEV
Transient Level sets the level of the transient part of the sound.
```

##### APPENDIX A: MACHINES

##### BASE

```
Noise Base sets the base frequency for the noise/transient filter.
```
```
WDTH
Noise Width sets the frequency width above the base frequency for the noise/transient filter.
```
```
GRAN
Noise grain adjusts the density of the grains for the noise part of the sound, from high density
(white noise) to low (grainy).
```
```
NLEV
Noise Level sets the level of the noise part of the sound.
```
#### A.2.3 WAVETONE

```
The WAVETONE machine is a two-oscillator synth engine with very flexible wave shaping possibilities,
borrowing elements from phase distortion, wavetables, ring modulation, hard sync, and adding flexible
noise design.
```
```
PAGE 1
```
##### TUN1

```
Osc1 Tune sets the pitch of oscillator 1.
```
```
WAV1
OSC1 Waveform sets the waveform for oscillator 1. The waveform crossfades between the differ-
ent waveforms. Which waveforms you can select here depends on what wavetable you select with
TBL1.
```
```
PD1
Osc1 Phase Distortion controls the amount of phase distortion of oscillator 1. Think of it as chang-
ing the pulse width, but on any waveform, not just square waves.
```
```
LEV1
Osc1 Level sets the level of oscillator 1.
```
```
TUN2
Osc2 Tune sets the pitch of oscillator 1.
```
```
WAV2
Osc2 Waveform sets the waveform for oscillator 2. The waveform crossfades between the differ-
ent waveforms. Which waveforms you can select here depends on what wavetable you select with
TBL2.
```
```
PD2
Osc2 Phase Distortion controls the amount of phase distortion of oscillator 2. Think of it as chang-
ing the pulse width, but on any waveform, not just square waves.
```
```
LEV2
Osc2 Level sets the level of oscillator 2.
```
```
Phase distortion makes the oscillator traverse the first and second half of the
waveform at different speeds, effectively distorting the waveshape by dragging its
midpoint towards the start or end. A simple example is the squarewave, where phase
distortion acts a pulsewidth parameter adjusting the duty cycle of the waveform.
```

##### APPENDIX A: MACHINES

##### PAGE 2

##### OFS1

```
Osc1 Lin Offset sets an offset of oscillator 1 tuning. This parameter offsets the frequency of the
oscillator linearly in hertz (unlike TUNE which adjusts the pitch in cents). This feature gives more
noticeable detune in lower notes than higher. It allows for a different kind of chorus-like detune,
since the beating frequency does not vary with the pitch.
```
```
TBL1
Osc1 Wavetable selects the wavetable for the oscillator 1 WAVE parameter. PRIM contains the ba-
sic oscillator waves Sin, Tri, Saw, Square. HARM contains a long list of waveforms sweeping across
other combinations of harmonics.
```
```
MOD
Oscillator Modulation selects different ways the two oscillators can interact with each other. The
settings are OFF, RING MOD, RING MODE FIXED (Osc 2 does not track note values), and HARD
SYNC.
```
```
In RING MOD mode, Oscillator 2 is modulating Oscillator 1. In that configuration, you
most often don’t want to hear the modulator, so you would turn down LEV2 to 0.
TUN1 will adjust the tuning of the sound, while TUN2 will change the sound. Even if
LEV2 is set to 0, adjusting WAV2 and PD2 will impact the timbre of the sound.
In HARD SYNC mode, Oscillator 1’s phase is reset every time Oscillator 2’s waveform
starts a new cycle. In this mode, TUN2 controls the tuning of the sound, and TUN1 will
control another aspect of the sound, that may sound a bit like a filter with resonance
when modulated.
```
```
RSET
Oscillator Phase Reset sets if and how the oscillator's wave phase resets when you play a note.
OFF does not reset the oscillator phase. ON resets the oscillators to the start of their waveform
phase. RAND resets the oscillators to a random position in the waveform phase.
```
```
OFS2
Osc2 Lin Offset sets an offset of oscillator 2 tune. This parameter offsets the frequency of the
oscillator linearly in hertz (unlike TUNE which adjusts the pitch in cents). This feature gives more
noticeable detune in lower notes than higher. It allows for a different kind of chorus-like detune,
since the beating frequency does not vary with the pitch.
```
```
TBL2
Osc2 Wavetable selects the wavetable for the oscillator 2 WAVE parameter. PRIM contains the ba-
sic oscillator waves Sin, Tri, Saw, Square. HARM contains a long list of waveforms sweeping across
other combinations of harmonics.
```
```
DRIF
Oscillator Drift sets the amount of random pitch drift of oscillator 1 and 2.
```

##### APPENDIX A: MACHINES

##### PAGE 3

##### AT K

```
Noise Attack sets the length of the attack phase of the noise amp envelope.
```
```
HOLD
Noise Hold sets the length of the hold phase of the noise amp envelope. Fixed Hold time values
(0–126) specify the length of the hold phase, and the envelope ignores Note Off events such as Trig
Length, releasing a [TRIG] key or a key on an external controller. Setting HOLD to NOTE means the
hold phase will be determined by Note On and Note Off events.
```
```
If you set HOLD to NOTE and use an external keyboard to trigger the envelope, then
the sound will be sustained (if DEC is set to less than 127) for as long as you press a
key on the keyboard.
```
```
DEC
Noise Decay sets the length of the decay phase of the noise amp envelope.
```
```
NLEV
Noise Level sets the level of the noise part of the sound.
```
```
BASE
Noise Base sets the base frequency for the noise filter.
```
```
WDTH
Noise Width sets the frequency width above the base frequency for the noise filter.
```
```
TYPE
Noise Type selects between three different types of noise, GRAIN, TUNED (a Sample and Hold that
tracks the pitch, and S&H (Sample and Hold).
```
```
CHAR
Noise Character changes the character of the noise. The effect of this parameter vary depending
on the selected TYPE.
GRAIN, Character sets the density of the grain, from white noise to very grainy.
TUNED, Character sets the base tuning of the sample & hold that are then tracked by pitch.
S&H, Character sets the fixed tuning of the sample and hold.
```
#### A.2.4 SWARMER

```
The SWARMER machine is based on using one main oscillator and a swarm of six additional detuned
oscillators to create a rich sound.
```
```
PAGE 1
```

##### APPENDIX A: MACHINES

##### TUNE

```
Tune offsets the incoming note value. This parameter is bipolar. A setting of 0 leaves the pitch
unchanged.
```
```
SWRM
Swarm Waveform sets the waveform of the detuned swarm oscillators.
```
```
DET
Detune sets the amount of detuning of the six swarm oscillators in relation to the main oscillator.
```
```
MIX
Mix sets the level of the swarm oscillators in relation to the main oscillator.
```
```
M.OCT
Main Octave detunes the main oscillator down one or two octaves but leaves the swarm oscillators’
pitch unchanged.
```
```
MAIN
Main Waveform sets the waveform of the main oscillator.
```
```
ANIM
Swarm Animation sets the modulation amount of the swarm oscillators. The swarm oscillators are
modulated separately in pairs by hidden LFOs running at different rates.
```
```
N.MOD
Noise Modulation sets the amount of noise modulation of the detuned swarm oscillators.
```
#### A.2.5 MIDI

The parameters for the MIDI machine are split over the TRIG, SYN, FLTR and AMP PARAMETER pages.
The FX page contains no parameters The MOD pages are the same as for audio tracks, but there are
two LFOs available for MIDI tracks.

```
TRIG PAGE 1
Set the actions for when a note is trigged. Change settings using the DATA ENTRY knobs. These
general settings affect note trigs placed in the sequencer.
```
##### NOTE

```
Trig Note sets the pitch of the note when trigged. When in LIVE RECORDING mode and playing
in KEYBOARD mode, the pitch of the [TRIG] keys played will override this setting. Press and turn
DATA ENTRY knob A to select only note values that exist in the scale set by KB SCALE. For more
information, please see “8.5.2 KEYBOARD SETUP MENU” on page 24.
```
```
VEL
Trig Velocity sets the velocity of the sequencer’s note trigs.
```
```
LEN
Trig Length sets the length of the note trig. In LIVE RECORDING mode, the duration of pressing the
[TRIG] keys overrides this general setting.
```
```
PROB
Trig Probability sets the probability that the trigs on the track plays or not. The probability outcome
is re-evaluated every time a trig is set to play. The default setting is 100%, meaning that all the trigs
on the track plays every time. This parameter can be parameter locked which lets you give separate
trigs their own probability.
```

##### APPENDIX A: MACHINES

##### L F O.T

```
LFO Trig controls if the LFO will be trigged or not.
```
```
FILL
Fill is a separate trig condition that determines if a trig is active (plays) or not depending on if the
device is in FILL mode or not. For more information, please see “10.12.4 FILL MODE” on page 50,
and “10.12.3 TRIG CONDITIONS AND CONDITIONAL LOCKS” on page 49.
ON, A trig with FILL set to ON, plays when FILL mode is active.
OFF, A trig with FILL set to OFF plays when FILL mode is not active.
```
```
The sequencer needs to be in FILL mode to activate the FILL trig condition. For more
information, please see “10.12.4 FILL MODE” on page 50.
```
##### COND

```
(Trig Condition) when you add a conditional lock, COND sets the Trig Condition with which a set of
conditional rules can be applied to any trig, using a conditional parameter lock. For more informa-
tion, please see “10.12.3 TRIG CONDITIONS AND CONDITIONAL LOCKS” on page 49.
```
```
SYN PAGE
Here you can set the MIDI channel that the MIDI machine should use to send data. Bank and program
change values are also set here, together with a few standard CC parameters. The default value of
the parameters on this page is OFF, meaning they are disabled and do not send out any data. Press
and hold [FUNC] and then press the DATA ENTRY knobs to enable them. You can then use the DATA
ENTRY knobs to set the parameter values as usual. Disable the parameters again by repeating the
activation procedure.
```
```
CHAN (Channe) sets the MIDI channel the track sends MIDI data to. If you set this parameter to
OFF, it turns the MIDI track off. Please note that this parameter cannot be parameter locked. (OFF,
1–16)
```
```
BANK (Bank) sends a bank change message on CC 0 MSB.
```
```
SBNK (Sub Bank) sends a bank change message on CC 32 LSB.
```
```
PROG (Program Change) sends a Program Change message.
```
```
PB (Pitch Bend) controls the pitch bend data sent on the MIDI track.
```
```
AT (Aftertouch) controls the aftertouch data sent on the MIDI track.
```
```
MW (Mod Wheel) controls the modulation wheel data sent on the MIDI track.
```
```
BC (Breath Controller) controls the breath control data sent on the MIDI track.
```
##### FLTR PAGE 1

```
Here you can set the values of the first eight assignable CC commands. The default value of the pa-
rameters on this page is OFF, meaning they are disabled and do not send out any data. Press and hold
[FUNC] and then press the DATA ENTRY knobs to enable them. You can then use the DATA ENTRY
knobs to set the parameter values as usual. Disable the parameters again by repeating the enabling
procedure.
```

##### APPENDIX A: MACHINES

```
VAL1-VAL8 (CC 1–8 Value) controls the values that the CC commands send. You specify the CC
commands themselves on FLTR page 2. These parameters default value is OFF. Press [FUNC]
+ DATA ENTRY knobs to activate the parameters and then turn the DATA ENTRY knobs to set a
value.
```
```
Here you can also name the parameter whose value you control. Press and hold
[FUNC] and then press and hold any DATA ENTRY knob to access the naming screen.
For more information, please see “6.5 THE NAMING SCREEN” on page 19.
```
FLTR PAGE 2

Here you select the first eight CC commands whose values you set with the parameters on the FLTR
page 1. Press the corresponding knob or [YES] to activate the CC command change.

```
SEL1-SEL8 (CC 1–8 Select) specifies the CC commands whose values you control controlled by
the parameters on the AMP PAGE 1 (CC VALUE) page. The selectable commands are the standard
MIDI Control Change Messages.
```
```
You can specify the CC command for the SEL parameters by sending the CC value from
the external MIDI equipped device you want to control.
```
1. On FLTR PAGE 2 or AMP PAGE 2 press and hold [FUNC] + DATA ENTRY knob A-H.
2. When the popup screen MIDI LEARN appears, and the parameter starts flashing,
    you can send a MIDI CC value on the active track’s MIDI channel (set in the SET-
    TINGS > MIDI CONFIG > CHANNELS menu) or on the auto channel.
3. The popup disappears and the parameter stops flashing when the sent CC value is
    received and configured on the selected SEL parameter. You can cancel MIDI learn
    by pressing [NO] twice.

AMP PAGE 1

Here you can set the values for eight additional assignable CC commands.

```
VAL9-VAL16 (CC 9–16 Value) controls the values that the CC commands send. You specify the
CC commands themselves on AMP page 2. These parameters default value is OFF. Press [FUNC]
+ DATA ENTRY knobs to activate the parameters and then turn the DATA ENTRY knobs to set a
value.
```
AMP PAGE 2

Here you select the eight additional CC commands whose values you set with the parameters on AMP
page 1. Press the corresponding knob or [YES] to activate the parameter change.

```
SEL9-SEL16 (CC 9–16 Select) specifies the CC commands whose values you control controlled by
the parameters on the AMP page 1 (CC VALUE) page. The selectable commands are the standard
MIDI Control Change Messages.
```

##### APPENDIX A: MACHINES

### A.3 FLTR MACHINES.

```
The FLTR machines are a collection of different filters and EQ.
```
#### A.3.1 MULTI-MODE

```
This filter lets you morph from a Lowpass to Bandpass to Highpass filter
```
##### AT K

```
Attack Time sets the length of the attack phase of the filter envelope.
```
```
DEC
Decay Time sets the length of the decay phase of the filter envelope.
```
```
SUS
Sustain Level sets the sustain level of the filter envelope.
```
```
REL
Release Time sets the length of the release phase of the filter envelope.
```
```
FREQ
Frequency sets the cutoff frequency of the multi-mode filter and the center frequency of the EQ.
```
```
RESO
Resonance sets the resonance behavior of the filter. Resonance introduces a peak in the spectrum at
the cutoff frequency. Gain sets the amount of boost/cut around the center frequency of the EQ.
```
```
TYPE
Type morphs the multimode filter from Lowpass to Bandpass to Highpass
```
```
ENV
Env. Depth sets the amount of cutoff frequency modulation from the filter envelope. The knob is bipo-
lar, both negative and positive modulation depths are available.
```
##### TIME

##### AMPLITUDE

```
Note On
```
```
Attack Decay
```
```
Sustain
```
```
Note Off
```
```
Env. Release
Delay
```

##### APPENDIX A: MACHINES

#### A.3.2 LOWPASS

This is a 4-pole low-pass filter with a 24 dB/octave slope.

##### AT K

```
Attack Time sets the length of the attack phase of the filter envelope.
```
```
DEC
Decay Time sets the length of the decay phase of the filter envelope.
```
```
SUS
Sustain Level sets the sustain level of the filter envelope.
```
```
REL
Release Time sets the length of the release phase of the filter envelope.
```
```
FREQ
Frequency sets the cutoff frequency of the filter.
```
```
RESO
Resonance sets the resonance behavior of the filter. Resonance introduces a peak in the spectrum at
the cutoff frequency.
```
```
ENV
Env. Depth sets the amount of cutoff frequency modulation from the filter envelope. The knob is bipo-
lar, both negative and positive modulation depths are available.
```
#### A.3.3 LEGACY LP/HP

The legacy filter is inspired by the filter from Digitone I and is a filter that is switchable between Lowpass
and Highpass.

##### AT K

```
Attack Time sets the length of the attack phase of the filter envelope.
```
```
DEC
Decay Time sets the length of the decay phase of the filter envelope.
```
```
SUS
Sustain Level sets the sustain level of the filter envelope.
```
```
REL
Release Time sets the length of the release phase of the filter envelope.
```
```
FREQ
Frequency sets the cutoff frequency of the filter.
```

##### APPENDIX A: MACHINES

##### RESO

```
Resonance sets the resonance of the filter. Resonance introduces a peak at the cutoff frequency.
```
```
TYPE
Filter Type lets you select between a Lowpass or Highpass filter, both a 2-pole, 12 dB/octave slope.
```
```
ENV
Env. Depth sets the amount of cutoff frequency modulation from the filter envelope. The knob is bipo-
lar, both negative and positive modulation depths are available.
```
#### A.3.4 COMB-

```
The comb filter introduces metallic sounding, pitch tuned, resonant overtones. The comb- filter has nega-
tive feedback and produces a more hollow, tube-like sound.
```
##### AT K

```
Attack Time sets the length of the attack phase of the filter envelope.
```
```
DEC
Decay Time sets the length of the decay phase of the filter envelope.
```
```
SUS
Sustain Level sets the sustain level of the filter envelope.
```
```
REL
Release Time sets the length of the release phase of the filter envelope.
```
```
FREQ
Frequency sets the resonant frequencies of the comb filter.
```
```
FDBK
Feedback sets the gain of the feedback signal. Please beware that setting FDBK to a high value can
create a very loud sound.
```
```
LPF
Low Pass Filter sets the cutoff frequency of the low pass filter in the feedback signal.
```
```
ENV
Env. Depth sets the amount of cutoff frequency modulation from the filter envelope. The knob is bipo-
lar, both negative and positive modulation depths are available.
```
#### A.3.5 COMB+

```
The comb filter introduces metallic sounding, pitch tuned, resonant overtones. The comb+ filter has posi-
tive feedback and produces a more string-like sound.
```

##### APPENDIX A: MACHINES

##### AT K

```
Attack Time sets the length of the attack phase of the filter envelope.
```
```
DEC
Decay Time sets the length of the decay phase of the filter envelope.
```
```
SUS
Sustain Level sets the sustain level of the filter envelope.
```
```
REL
Release Time sets the length of the release phase of the filter envelope.
```
```
FREQ
Frequency sets the resonant frequencies of the comb filter.
```
```
FDBK
Feedback sets the gain of the feedback signal. Please beware that setting FDBK to a high value can
create a very loud sound.
```
```
LPF
Low Pass Filter sets the cutoff frequency of the low pass filter in the feedback signal.
```
```
ENV
Env. Depth sets the amount of cutoff frequency modulation from the filter envelope. The knob is bipo-
lar, both negative and positive modulation depths are available.
```
#### A.3.6 EQUALIZER

This machine is a parametric Equalizer.

##### AT K

```
Attack Time sets the length of the attack phase of the EQ envelope.
```
```
DEC
Decay Time sets the length of the decay phase of the EQ envelope.
```
```
SUS
Sustain Level sets the sustain level of the EQ envelope.
```
```
REL
Release Time sets the length of the release phase of the EQ envelope.
```
```
FREQ
Frequency sets the center frequency of the EQ.
```
```
GAIN
Gain sets the amount of boost/cut around the center frequency of the EQ.
```
```
Q
Q lets you select the bandwidth of the frequency range that the EQ affects. The higher the Q value,
the narrower the bandwidth. A broad bandwidth boosts or cuts a larger band of frequencies than a
narrow.
```

##### APPENDIX A: MACHINES

##### ENV

```
Env. Depth sets the amount of cutoff frequency modulation from the filter envelope. The knob is bipo-
lar, both negative and positive modulation depths are available.
```
```
The parametric EQ is used to boost desired frequencies or to remove unwanted fre-
quencies of the sound.
```
```
The ATK, DEC, SUS, and REL parameters only affects the selected filter machine. Not the
base-width filter on FLTR page 2.
```

## APPENDIX B: THE FM TONE SYNTHESIS

APPENDIX B: THE FM TONE SYNTHESIS

### B.1 OVERVIEW

The Digitone II's FM TONE machine is a four operator Frequency Modulation (FM) synth in the style of the
classic 80s implementations. However, unlike the early FM synths, the Digitone II use its FM engine more

like a complex tone generator than a complete synthesizer voice (although it does have this capability too).
The Digitone II signal path is more similar to a typical subtractive synth than a classic FM voice.

```
FM Overdrive Multimode
Filter
```
```
Base-width
Filter
```
```
Amp
```
The idea of this design is to harness the raw, and often complex, soundscapes of FM synthesis and use a
more well-known and approachable subtractive method for the overall sound shaping.

FM is a synthesis method where you add harmonics to the timbre by using modulation or layering to shape
the sound. You add harmonics by modulating one oscillator’s pitch with the output of another oscillator. FM
works similarly to how you would apply vibrato via an LFO. At slower modulation rates, it is simply vibrato,
but when the modulating frequency reaches audio rate, the vibrato effects becomes a part of the sound and
becomes a timbral effect instead.

### B.2 OPERATORS

In FM synthesis the oscillators are called operators. Unlike the analog oscillator, the operator also contains
an envelope and specific input and outputs, making it a sort of macro oscillator. FM combines two or more
operators to generate a more harmonically rich output. An operator that is used to modulate another opera-
tor is called a modulator. The operator that generates, or carries, the resulting tone is called a carrier.

Feedback is used to increase the sharpness of a sound and is usually only applied to modulators. The
output of the operator is fed back into itself, resulting in added harmonics. For example, have a look at the
Sounds B001 and B002 in the Digitone II to see how feedback is used to create a basic square and saw
waveform. For more information, please see “A.2.1 FM TONE” on page 87.

```
Operator
Waveform Envelope
```
```
Feedback
```
```
Modulation
input Output
```
```
Amplitude
```
```
Wavelength
(frequency)
```
```
Output
```
```
Modulator
(ratio: 8)
```
```
Carrier
(ratio: 1)
```

##### APPENDIX B: THE FM TONE SYNTHESIS

```
In some instances, an operator can be both carrier and modulator. This means that it outputs its result as a
tone while also using it to modulate another operator. We have divided the operators into three groups: C, A,
and B (B1 and B2) to lessen the complexity and make the Digitone II easier to use. Since group B consist of
two operators, the parameter controls for B are macro mapped to both operators.
```
### B.3 ALGORITHMS

```
An algorithm is a set routing, or combination, of the operators. Routing the modulation in different ways
gives you several different methods of applying modulation and naturally different results.
```
```
Modulator Envelope Carrier
```
```
Feedback
```
```
Feedback
```
```
Modulator
```
```
Envelope output
```
```
Carrier
```
```
Direct output
```
```
Output
```
```
Algorithm
```
```
Modulation
Level
```
```
Pictured above is a 2 operator algorithm, which is FM synthesis in its most basic form. The Digitone II has
four operators available which can be routed in many different ways. Different algorithms dramatically
changes the characteristics of the output - for example stacking four operators can lead to very complex
timbres.
The Digitone II has eight different algorithms where the four operators are routed in different ways. For
more information, please see “A.2.1 FM TONE” on page 87. Each algorithm has two carrier outputs (X
and Y) that come from two different operators depending on what algorithm you chose. It is possible to
use the MIX parameter to mix between these outputs so that you can cross-fade between two separate
timbres. For more information, please see “A.2.1 FM TONE” on page 87.
```
```
The lines going to X and Y indicate the output from a carrier. There are two different ways a carrier output
is sent to the X/Y outputs: 1. Its amplitude is unaffected by the operator envelope (dotted line). 2. Its ampli-
tude is affected by the operator envelope (filled line). The sound from the X/Y outputs is then routed via the
overdrive to the filters.
```
### B.4 FM RATIOS

```
Frequency modulation is applied in multipliers of the principal frequency to retain the sounds tonality. These
multipliers are known as ratios. Each operator group’s ratio is a multiplier of the input pitch (note value). The
higher the ratio, the higher the pitch frequency. For example, when applying modulation with a ratio of 1:2
(carrier:modulator), the carrier output resembles a square wave. A ratio of 1:1 sounds like a sawtooth, and
odd numbers can be used for various metallic or other “natural” sounds. In Digitone II, the FM ratios for the
different operator groups works like this:
```

##### APPENDIX B: THE FM TONE SYNTHESIS

C which always works like a carrier, is limited mostly to integers since it is generally used for carrying the
base note of the sound. For more information, please see “A.2.1 FM TONE” on page 87.

A has a more extensive number of ratio values to allow for more inharmonic relationships. For more informa-
tion, please see “A.2.1 FM TONE” on page 87.

B (B1 and B2) controls both operators at the same time. The minimum value for B1 and B2 is .25. As you
turn the encoder, B2 increases until it reaches the max (16). It then starts over from .25 and B1 increases to
the next value (0.5). This revolving behavior continues until both operators reach the maximum value. This
parameter behavior is similar to the movement of the hands on a watch. For more information, please see
“A.2.1 FM TONE” on page 87.

### B.5 OPERATOR ENVELOPES

If you modulate one oscillator directly with another it creates very harsh harmonics, so you need to limit the
modulation level to control the amount of modulation. In Digitone II you use an envelope and a LEVEL pa-
rameter control to attenuate the amount of how much the modulator affects the carrier. The envelopes also
give the possibility to control the modulation over time. A piano for example, when first struck, the timbre is
sharp but quickly fades to a softer tone. Envelopes and modulation level are vital elements in FM synthesis
and are the tools that you use to shape the sounds you want to achieve.

The Digitone II FM engine has two operator envelopes that are designed to be practical and easy to use.
One envelope is for operator group A, and one is for group B (B1 and B2).

The envelopes are essentially expanded AD (Attack Decay) envelopes, but with an added adjustable end
level (the amplitude level the sound reaches at the end of the decay stage). Normally, an AD envelope al-
ways ends at zero level, but with FM you often want to retain some modulation after a short pluck or fade in
for example. For more information, please see “A.2.1 FM TONE” on page 87.

```
Please note that operator envelope B controls the output from both B1 and B2.
```
The envelopes can be either triggered or gated - making it either an ADE (Attack Decay End) or an ASDE
(Attack Sustain Decay End) envelope. The sustain phase does not have an envelope level. It is instead the
LEV parameter that sets the sustain level. The note length defines the length of the sustain phase. For more
information, please see “A.2.1 FM TONE” on page 87.

Trigged (ATRG/BTRG ON)

### Attack Decay End level

```
Note on
```
Gated (ATRG/BTRG OFF)

### Attack Sustain Decay End level

```
Note on Note off
```
You can also set if the envelopes should reset or not when they are retrigged. For more information, please
see “A.2.1 FM TONE” on page 87.


##### APPENDIX B: THE FM TONE SYNTHESIS

```
Reset on (ARST/BRST ON)
```
```
Trig Trig
```
### Reset on

### Reset off

```
Trig Trig
```
```
Reset off (ARST/BRST OFF)
```
```
Trig Trig
```
### Reset on

### Reset off

```
Trig Trig
```
```
The LEVEL parameters on the SYN2 page controls the amount of modulation from the A and B operators.
For more information, please see “A.2.1 FM TONE” on page 87. The LEVEL parameter for B is macro
mapped to both operator B1 and B2 and control their modulation amount as per this graph:
```
```
B1
B2
```
```
Level
Parameter value 43 64 85
```
##### 0

##### 127

### B.6 HARMONICS

```
The default output from an operator is a sine wave, but you can use the HARM parameter to add upper
partials to some of the operators’ sine waves to create more harmonically rich waveforms. The HARM pa-
rameter is bipolar. Negative parameter values change the harmonics of operator C, while positive parameter
values change the harmonics of operators A and B1.
```
Harm - Harm +

```
When changing harmonics, intermediate values interpolates between the current and the next harmonic.
This interpolation works much like in wavetable synthesis, sweeping between the harmonics, smoothly
transitioning from one timbre to another. For more information, please see “A.2.1 FM TONE” on page 87.
The harmonic series for the operators looks like this:
```

##### APPENDIX B: THE FM TONE SYNTHESIS

```
Saw
(All partials)
```
```
Saw reduction
```
```
Odd/Even Mix
```
```
Square
(Odd partials)
```
```
Square
reduction
```
```
Bell
```
The Digitone II uses a form of additive synthesis to create the harmonic series for the HARM parameter.
Additive synthesis is one of the oldest forms of electronic sound generation. It is a quite simple form of
synthesis but still very powerful. The basic principle is to add multiple sine waves together to form complex
timbres. Each sine wave is called a partial. By attenuating each partial, the timbre changes its harmonic
content, resulting in different waveforms.

The first partial is always kept
at full volume, which keeps the
base octave intact.

```
The first partial is always
kept at full volume,
which keeps the base
octave intact.
```
```
Adding every partial in
series will replicate a
sawtooth. Note how the
volume decreases for
each partial, creating a
natural falloff.
```
```
Adding every odd partial in
series will approximate a
squarewave.
```
```
This method can be used
to make all sorts of timbres.
This for example sounds
close to a bell tone.
```
Adding every partial in series
will replicate a sawtooth. Note
how the volume decreases for
each partial, creating a natural
falloff.

```
The first partial is always
kept at full volume,
which keeps the base
octave intact.
```
```
Adding every partial in
series will replicate a
sawtooth. Note how the
volume decreases for
each partial, creating a
natural falloff.
```
```
Adding every odd partial in
series will approximate a
squarewave.
```
```
This method can be used
to make all sorts of timbres.
This for example sounds
close to a bell tone.
```
Adding every odd partial in
series will approximate a
square wave.

```
The first partial is always
kept at full volume,
which keeps the base
octave intact.
```
```
Adding every partial in
series will replicate a
sawtooth. Note how the
volume decreases for
each partial, creating a
natural falloff.
```
```
Adding every odd partial in
series will approximate a
squarewave.
```
```
This method can be used
to make all sorts of timbres.
This for example sounds
close to a bell tone.
```
The additive method can be
used to make many different
timbres. This combination of
partials, for example, sounds
close to a bell tone.

```
The first partial is always
kept at full volume,
which keeps the base
octave intact.
```
```
Adding every partial in
series will replicate a
sawtooth. Note how the
volume decreases for
each partial, creating a
natural falloff.
```
```
Adding every odd partial in
series will approximate a
squarewave.
```
```
This method can be used
to make all sorts of timbres.
This for example sounds
close to a bell tone.
```

##### APPENDIX B: THE FM TONE SYNTHESIS

### B.7 SYN PAGE 1 PARAMETERS OVERVIEW

```
Here is a graphical overview of what part of the FM engine the parameters on the SYN1 pages affects. The
affected part of the FM engine is highlighted with grey. For more information, please see “A.2.1 FM TONE”
on page 87.
```
Detune

Ratio C Ratio A Ratio B

Harm - Harm + Feedback Mix


## APPENDIX C: MIDI

APPENDIX C: MIDI

This appendix lists the CC and NRPN specification for the Digitone II.

### C.1 TRACK PARAMETERS

##### TRACK

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Mute 94 1 108
Track level 95 1 110
```
### C.2 TRIG PARAMETERS

##### TRIG PARAMETERS

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Note 3 3 0
Velocity 4 3 1
Length 5 3 2
Filter Trig 13
LFO Trig 14
Portamento Time 9 3 6
Portamento On/Off 65 3 7
```
### C.3 SOURCE PARAMETERS

##### SYN PAGE 1

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Data entry knob A (machine dependent) 40 1 73
Data entry knob B (machine dependent) 41 1 74
Data entry knob C (machine dependent) 42 1 75
Data entry knob D (machine dependent) 43 1 76
Data entry knob E (machine dependent) 44 1 77
Data entry knob F (machine dependent) 45 1 78
Data entry knob G (machine dependent) 46 1 79
Data entry knob H (machine dependent) 47 1 80
```
##### SYN PAGE 2

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Data entry knob A (machine dependent) 48 1 81
Data entry knob B (machine dependent) 49 1 82
Data entry knob C (machine dependent) 50 1 83
Data entry knob D (machine dependent) 51 1 84
Data entry knob E (machine dependent) 52 1 85
Data entry knob F (machine dependent) 53 1 86
Data entry knob G (machine dependent) 54 1 87
Data entry knob H (machine dependent) 55 1 88
```
##### SYN PAGE 3

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Data entry knob A (machine dependent) 56 1 89
Data entry knob B (machine dependent) 57 1 90
Data entry knob C (machine dependent) 58 1 91
```

##### APPENDIX C: MIDI

##### SYN PAGE 3

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Data entry knob D (machine dependent) 59 1 92
Data entry knob E (machine dependent) 60 1 93
Data entry knob F (machine dependent) 61 1 94
Data entry knob G (machine dependent) 62 1 95
Data entry knob H (machine dependent) 63 1 96
```
##### SYN PAGE 4

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Data entry knob A (machine dependent) 70 1 97
Data entry knob B (machine dependent) 71 1 98
Data entry knob C (machine dependent) 72 1 99
Data entry knob D (machine dependent) 73 1 100
Data entry knob E (machine dependent) 74 1 101
Data entry knob F (machine dependent) 75 1 102
Data entry knob G (machine dependent) 76 1 103
Data entry knob H (machine dependent) 77 1 104
```
### C.4 FILTER PARAMETERS

##### FILTER

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Frequency 16 1 20
Data entry knob F (machine dependent) 17 1 21
Data entry knob G (machine dependent) 18 1 22
Env. Depth 24 1 26
Attack Time 20 1 16
Decay Time 21 1 17
Sustain Level 22 1 18
Release Time 23 1 19
Env. Delay 19 1 23
Key Tracking 26 1 69
Base 27 1 24
Width 28 1 25
Env. Reset 25 1 68
```
### C.5 AMP PARAMETERS

##### AMP

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Attack Time 84 1 30
Hold Time 85 1 31
Decay Time 86 1 32
Sustain Level 86 1 33
Release Time 88 1 34
Env. Reset 92 1 41
Mode 91 1 40
Pan 89 1 38
Volume 90 1 39
```

##### APPENDIX C: MIDI

### C.6 EUCLIDEAN SEQUENCER PARAMETERS

##### AMP

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Pulse Generator 1 3 8
Pulse Generator 2 3 9
Euclidean Mode On/Off 3 14
Rotation Generator 1 3 11
Rotation Generator 2 3 12
Track Rotation 3 13
Boolean Operator 3 10
```
### C.7 FX PARAMETERS

##### AMP

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Bit Reduction 78 1 5
Overdrive 81 1 8
Sample Rate Reduction 79 1 6
SRR Routing 80 1 7
Overdrive Routing 82 1 9
Delay Send 30 1 36
Reverb Send 31 1 37
Chorus Send 29 1 35
```
### C.8 MOD PARAMETERS.

##### LFO 1

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Speed 102 1 42
Multiplier 103 1 43
Fade In/Out 104 1 44
Destination 105 1 45
Waveform 106 1 46
Start Phase/Slew 107 1 47
Trig Mode 108 1 48
Depth 109 1 49
```
##### LFO 2

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Speed 111 1 50
Multiplier 112 1 51
Fade In/Out 113 1 52
Destination 114 1 53
Waveform 115 1 54
Start Phase/Slew 116 1 55
Trig Mode 117 1 56
Depth 118 1 57
```

##### APPENDIX C: MIDI

##### LFO 3

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Speed 1 58
Multiplier 1 59
Fade In/Out 1 60
Destination 1 61
Waveform 1 62
Start Phase/Slew 1 70
Trig Mode 1 71
Depth 1 72
```
### C.9 SEND FX PARAMETERS

##### DELAY

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Delay Time 21 2 0
Pingpong 22 2 1
Stereo Width 23 2 2
Feedback 24 2 3
Highpass Filter 25 2 4
Lowpass Filter 26 2 5
Reverb Send 27 2 6
Mix Volume 28 2 7
```
##### REVERB

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Predelay 29 2 8
Decay Time 30 2 9
Shelving Freq 31 2 10
Shelving Gain 89 2 11
Highpass Filter 90 2 12
Lowpass Filter 91 2 13
Mix Volume 92 2 15
```
##### CHORUS

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Depth 16 2 41
Speed 9 2 42
High Pass Filter 70 2 43
Width 71 2 44
Delay Send 12 2 45
Reverb Send 13 2 46
Mix Volume 14 2 47
```

##### APPENDIX C: MIDI

### C.10 MIXER PARAMETERS

##### COMPRESSOR

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Threshold 111 2 16
Attack Time 112 2 17
Release Time 113 2 18
Makeup Gain 114 2 19
Pattern Volume 119 2 24
Ratio 115 2 20
Sidechain Source 116 2 21
Sidechain Filter 117 2 22
Dry/Wet Mix 118 2 23
```
##### EXTERNAL IN MIXER

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Dual Mono 82 2 40
Input L Level 72 2 30
Input L Pan 74 2 32
Input R Level 73 2 31
Input R Pan 75 2 33
Input L Delay Send 78 2 36
Input R Delay Send 79 2 37
Input L Reverb Send 80 2 38
Input R Reverb Send 81 2 39
Input L Chorus Send 76 2 34
Input R Chorus Send 77 2 35
Input L R Level 72 2 30
Input L R Balance 74 2 32
Input L R Delay Send 78 2 36
Input L R Reverb Send 80 2 38
Input L R Chorus Send 76 2 34
```
### C.11 VAL PARAMETERS

These are the CC VAL parameters on the [FLTR] and [AMP] pages for MIDI tracks.

```
CC VAL
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
VAL1 70 1 16
VAL2 71 1 17
VAL3 72 1 18
VAL4 73 1 19
VAL5 74 1 20
VAL6 75 1 21
VAL7 76 1 22
VAL8 77 1 23
VAL9 78 1 60
VAL10 79 1 61
```

##### APPENDIX C: MIDI

##### CC VAL

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
VAL11 80 1 62
VAL12 81 1 63
VAL13 82 1 64
VAL14 83 1 65
VAL15 84 1 66
VAL16 85 1 67
```
### C.12 MISC PARAMETERS

##### MISC

```
Parameter CC MSB CC LSB NRPN MSB NRPN LSB
Pattern Mute 110 1 109
Master Overdrive 17 2 50
```

## APPENDIX D: LFO MODULATION DESTINATIONS

APPENDIX D: LFO MODULATION DESTINATIONS

The following are the modulation destinations for the Digitone II’s LFOs:

AUDIO TRACKS

META: None

LFO1: Speed (Only available for LFO2/3)

LFO1: Multiplier (Only available for LFO2/3)

LFO1: Fade In/Out (Only available for LFO2/3)

LFO1: Waveform (Only available for LFO2/3)

LFO1: Start Phase (Only available for LFO2/3)

LFO1: Trig Mode (Only available for LFO2/3)

LFO1: Depth (Only available for LFO2/3)

LFO2: Speed (Only available for LFO3)

LFO2: Multiplier (Only available for LFO3)

LFO2: Fade In/Out (Only available for LFO3)

LFO2: Waveform (Only available for LFO3)

LFO2: Start Phase (Only available for LFO3)

LFO2: Trig Mode (Only available for LFO3)

LFO2: Depth (Only available for LFO3)

SYN: Data entry knob A, page 1– 4
(machine dependent.)

SYN: Data entry knob B, page 1– 4
(machine dependent)

SYN: Data entry knob C, page 1– 4
(machine dependent)

SYN: Data entry knob D, page 1– 4
(machine dependent)

SYN: Data entry knob E, page 1– 4
(machine dependent)

SYN: Data entry knob F, page 1– 4
(machine dependent)

SYN: Data entry knob G, page 1– 4
(machine dependent)

SYN: Data entry knob H, page 1– 4
(machine dependent)

FILTER: Attack Time

FILTER: Decay Time

FILTER: Sustain Level

FILTER: Release Time

FILTER: Frequency

FILTER: Data entry knob F (machine dependent)

FILTER: Data entry knob G (machine dependent)

FILTER: Envelope Depth

FILTER: Env. Delay

FILTER: Key Tracking

```
FILTER: Base
FILTER: Width
FILTER: Env. Reset
```
```
AMP: Attack Time
AMP: Hold Time
AMP: Decay Time
AMP: Sustain Level
AMP: Release Time
AMP: Pan
AMP: Volume
```
```
FX: Delay Send
FX: Reverb Send
FX: Chorus Send
FX: Bit Reduction
FX: SRR
FX: SRR Routing
FX: Overdrive
```
##### MIDI TRACKS

```
META: None
```
```
LFO1: Speed (Only available for LFO2)
LFO1: Multiplier (Only available for LFO2)
LFO1: Fade In/Out (Only available for LFO2)
LFO1: Waveform (Only available for LFO2)
LFO1: Start Phase (Only available for LFO2)
LFO1: Trig Mode (Only available for LFO2)
LFO1: Depth (Only available for LFO2)
```
```
SYN: Pitch Bend
SYN: Aftertouch
SYN: Mod Wheel
SYN: Breath Controller
```
```
CC: CC1–16 Values
```

## APPENDIX E: KEYBOARD SCALES

APPENDIX E: KEYBOARD SCALES

```
The following are the selectable scales for the KEYBOARD mode. For more information, please see: “8.5.1
KEYBOARD MODE” on page 24,
```
##### • CHROMATIC

##### • IONIAN (MAJOR)

##### • DORIAN

##### • PHRYGIAN

##### • LYDIAN

##### • MIXOLYDIAN

##### • AEOLIAN (MINOR)

##### • LOCRIAN

##### • PENTATONIC MINOR

##### • PENTATONIC MAJOR

##### • MELODIC MINOR

##### • HARMONIC MINOR

##### • WHOLE TONE

##### • BLUES

##### • COMBO MINOR

##### • PERSIAN

##### • I WATO

##### • IN-SEN

##### • HIRAJOSHI

##### • PELOG

##### • PHRYGIAN DOMINANT

##### • WHOLE-HALF DIMINISHED

##### • HALF-WHOLE DIMINISHED

##### • SPANISH

##### • MAJOR LOCRIAN

##### • SUPER LOCRIAN

- DORIAN b 2
- LYDIAN AUGMENTED
- LYDIAN DOMINANT
- DOUBLE HARMONIC MAJOR
- LYDIAN #2 #6
- ULTRAPHRYGIAN
- HUNGARIAN MINOR
- ORIENTAL
- IONIAN #2 #5
- LOCRIAN bb 3 bb 7


## INDEX

INDEX

##### +DRIVE 16 , 28

### A

##### ARPEGGIATOR 38

##### AUDIO ROUTING

```
Global 75
```
### B

##### BACKUP 20

##### BREATH CONTROLLER 38

### C

##### CHAINS 39

##### CHORUS 65

##### CHROMATIC MODE 24

##### CLASS COMPLIANT 20

##### COMPRESSOR 65

##### CONDITIONAL LOCKS 49

##### CONNECTORS 14

##### CONTROL ALL 19

Configuration 34
COPY, PASTE AND CLEAR 50

CREDITS AND CONTACT INFORMATION 86

### D

##### DATA STRUCTURE 16

Patterns 16
Presets 16
Project 16
DELAY 63

### E

##### EARLY STARTUP MENU 79

##### EUCLIDEAN SEQUENCER 44

### F

##### FACTORY RESET 79

##### FILL MODE 50

##### FILTER 57

##### FLTR MACHINES 100

##### FM SYNTHESIS

Algorithms 106
Carrier 105
FM ratios 106
Harmonics 108
Modulator 105
Operator envelopes 107
Operators 105
SYN1 Page 1 parameters overview 110
FX AND MIXER PARAMETERS 63

```
Chorus 65
Compressor 65
Delay 63
External mixer 68
FX mixer 68
```
```
Internal mixer 67
Reverb 64
```
### G

##### GRID RECORDING MODE 41

### H

##### HARD SYNC 95

### K

##### KEYBOARD SCALE 25

##### KEYBOARD SCALES 118

##### KEY COMBINATIONS 82

##### KITS 28

### L

##### LED BACKLIGHT 76

##### LED INTENSITY 76

##### LFO 61 , 62

```
Modulation destinations 117
```
### M

##### MACHINES 16 , 87

```
Assigning to track 87
METRONOME 23
MICRO TIMING 46
MIDI CC & NRPN 111
MIDI CONFIG 72
MIDI LEARN 99
MIDI TRACKS 17
Parameters 97
MIXER 67 , 68
MUTE MODE 25
```
### N

##### NAMING SCREEN 19

##### NOTE EDIT 41

##### NOTE EDIT MENU 41

### O

##### OS UPGRADE 78

### P

##### PAGE SETUP

```
Length per pattern mode 46
Length per track mode 47
PANEL LAYOUT 12
PARAMETER LOCKS 48
PATTERNS
Grid recording mode 41
Live recording mode 42
Parameter locks 48
Pattern control 39
Selecting a pattern 39
Trig modes 26
```

##### APPENDIX E: KEYBOARD SCALES

```
Trig Types 40
PATTERNS, KITS AND PRESETS 28
Editing a preset 33
Playing a preset 33
PERFORM KIT MODE 53
PORTAMENTO 36
On/Off 57
PRESET LOCKS 48
PRESETS 28
Audio track parameters 55
Editing a preset 33
Playing a preset 33
Saving a preset 34
PRESET SELECTING 22
PROJECT MANAGER 70
PROJECTS 70
Load 70
Project Manager 70
Save 70
Write protection 70
PURGE (REMOVE UNUSED)
All 70
Kits 33
```
### Q

##### QUANTIZATION 46

##### QUICK SCROLLING 19

##### QUICK START 22

### R

##### RETRIGS 46 , 56

##### REVERB 64

##### RING MODULATION 95

### S

##### SAFETY AND MAINTENANCE 3

##### SCALES 118

##### SCREEN SAVER 18

##### SEQUENCER 39

```
Conditional locks 49
Editing a pattern 40
Parameter locks 48
Pattern control 39
Retrig Menu 46
Scale Menu 46
Selecting a pattern 39
Trig menu 44
Trig parameters page 46
SEQUENCER RECORDING MODES
Grid recording mode 41
Live recording mode 42
SETTINGS 70
MIDI config 71 , 72
Project 70
System 76 , 77
SETUP EXAMPLES 80
```
```
Controlling a synthesizer using the MIDI tracks 80
Digitone II with a monophonic bass machine 80
SONG MODE 51
SOUND ARCHITECTURE 15
SWING 23
SYN MACHINES 87
SYSEX DUMP 74
```
### T

##### TECHNICAL INFORMATION 85

##### TEMPO 23

##### TEMPORARY SAVE AND RELOAD 50

##### TRACK PARAMETERS 55

```
Amp 57
Filter 57
LFO 60
Syn 57
Trig 55 , 56
TRANSPOSE
Pattern transpose 51
Track transpose 51
TRIG MODE 26
TRIGS 40
Lock trigs 40
Note trigs 40
Parameter locks 48
```
### U

##### UNISON 45

### V

##### VOICE MENU 45

```
Locked voices 45
Unison 45
Voice stealing 42 , 45
```

##### APPENDIX E: KEYBOARD SCALES


DOC10093enA


