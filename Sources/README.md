# Sources

This directory contains all Swift source code modules for the DigitonePad application.

## Modules

- **AppShell**: Main application shell and coordination
- **AudioEngine**: Core audio processing and routing
- **DataLayer**: Data persistence and model definitions
- **SequencerModule**: Pattern sequencing and timing
- **VoiceModule**: Sound synthesis components
- **FilterModule**: Audio filtering components
- **FXModule**: Audio effects processing
- **MIDIModule**: MIDI input/output handling
- **UIComponents**: Reusable UI elements
- **MachineProtocols**: Interface definitions for audio components

Each module is structured as a Swift Package to enable modular development and clear dependency management. 