<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model name="DigitonePad" userDefinedModelVersionIdentifier="" type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22770" systemVersion="24.0.0" minimumToolsVersion="11.0">
    <entity name="Project" representedClassName="Project">
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="updatedAt" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="patterns" toMany="YES" destinationEntity="Pattern" inverseName="project" inverseEntity="Pattern" deletionRule="Cascade"/>
        <relationship name="kits" toMany="YES" destinationEntity="Kit" inverseName="project" inverseEntity="Kit" deletionRule="Cascade"/>
        <relationship name="presets" toMany="YES" destinationEntity="Preset" inverseName="project" inverseEntity="Preset" deletionRule="Cascade"/>
    </entity>
    <entity name="Pattern" representedClassName="Pattern">
        <attribute name="name" attributeType="String"/>
        <attribute name="length" attributeType="Integer 16" defaultValueString="64"/>
        <attribute name="tempo" attributeType="Double" defaultValueString="120.0"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="project" toMany="NO" destinationEntity="Project" inverseName="patterns" inverseEntity="Project" deletionRule="Nullify"/>
        <relationship name="tracks" toMany="YES" destinationEntity="Track" inverseName="pattern" inverseEntity="Track" deletionRule="Cascade"/>
        <relationship name="kit" toMany="NO" destinationEntity="Kit" inverseName="patterns" inverseEntity="Kit" deletionRule="Nullify"/>
        <relationship name="trigs" toMany="YES" destinationEntity="Trig" inverseName="pattern" inverseEntity="Trig" deletionRule="Cascade"/>
    </entity>
    <entity name="Track" representedClassName="Track">
        <attribute name="name" attributeType="String" defaultValueString="Track"/>
        <attribute name="volume" attributeType="Float" defaultValueString="0.75" minValueString="0.0" maxValueString="1.0"/>
        <attribute name="pan" attributeType="Float" defaultValueString="0.0" minValueString="-1.0" maxValueString="1.0"/>
        <attribute name="isMuted" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="isSolo" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="trackIndex" attributeType="Integer 16" defaultValueString="0" minValueString="0" maxValueString="15"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="pattern" toMany="NO" destinationEntity="Pattern" inverseName="tracks" inverseEntity="Pattern" deletionRule="Nullify"/>
        <relationship name="kit" toMany="NO" destinationEntity="Kit" inverseName="tracks" inverseEntity="Kit" deletionRule="Nullify"/>
        <relationship name="preset" toMany="NO" destinationEntity="Preset" inverseName="tracks" inverseEntity="Preset" deletionRule="Nullify"/>
        <relationship name="trigs" toMany="YES" destinationEntity="Trig" inverseName="track" inverseEntity="Trig" deletionRule="Cascade"/>
    </entity>
    <entity name="Kit" representedClassName="Kit">
        <attribute name="name" attributeType="String"/>
        <attribute name="soundFiles" attributeType="Transformable" valueTransformerName="NSSecureUnarchiveFromDataTransformer" customClassName="[String]"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="patterns" toMany="YES" destinationEntity="Pattern" inverseName="kit" inverseEntity="Pattern" deletionRule="Nullify"/>
        <relationship name="project" toMany="NO" destinationEntity="Project" inverseName="kits" inverseEntity="Project" deletionRule="Nullify"/>
        <relationship name="tracks" toMany="YES" destinationEntity="Track" inverseName="kit" inverseEntity="Track" deletionRule="Nullify"/>
    </entity>
    <entity name="Preset" representedClassName="Preset">
        <attribute name="name" attributeType="String"/>
        <attribute name="category" attributeType="String" optional="YES"/>
        <attribute name="settings" attributeType="Transformable" valueTransformerName="NSSecureUnarchiveFromDataTransformer"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="project" toMany="NO" destinationEntity="Project" inverseName="presets" inverseEntity="Project" deletionRule="Nullify"/>
        <relationship name="tracks" toMany="YES" destinationEntity="Track" inverseName="preset" inverseEntity="Track" deletionRule="Nullify"/>
    </entity>
    <entity name="Trig" representedClassName="Trig">
        <attribute name="step" attributeType="Integer 16" defaultValueString="0" minValueString="0" maxValueString="127"/>
        <attribute name="isActive" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="note" attributeType="Integer 16" defaultValueString="60" minValueString="0" maxValueString="127"/>
        <attribute name="velocity" attributeType="Integer 16" defaultValueString="100" minValueString="1" maxValueString="127"/>
        <attribute name="duration" attributeType="Float" defaultValueString="1.0" minValueString="0.1" maxValueString="16.0"/>
        <attribute name="probability" attributeType="Integer 16" defaultValueString="100" minValueString="0" maxValueString="100"/>
        <attribute name="microTiming" attributeType="Float" defaultValueString="0.0" minValueString="-50.0" maxValueString="50.0"/>
        <attribute name="retrigCount" attributeType="Integer 16" defaultValueString="0" minValueString="0" maxValueString="8"/>
        <attribute name="retrigRate" attributeType="Float" defaultValueString="0.25"/>
        <attribute name="pLocks" attributeType="Transformable" valueTransformerName="NSSecureUnarchiveFromDataTransformer" optional="YES"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="updatedAt" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="track" toMany="NO" destinationEntity="Track" inverseName="trigs" inverseEntity="Track" deletionRule="Nullify"/>
        <relationship name="pattern" toMany="NO" destinationEntity="Pattern" inverseName="trigs" inverseEntity="Pattern" deletionRule="Nullify"/>
    </entity>
    <elements>
        <element name="Project" positionX="-63" positionY="-16" width="128" height="90"/>
        <element name="Pattern" positionX="89" positionY="-29" width="128" height="134"/>
        <element name="Track" positionX="268" positionY="-22" width="128" height="224"/>
        <element name="Kit" positionX="92" positionY="111" width="128" height="60"/>
        <element name="Preset" positionX="-63" positionY="111" width="128" height="134"/>
        <element name="Trig" positionX="447" positionY="-22" width="128" height="254"/>
    </elements>
</model> 