import Foundation
import Accelerate
import MachineProtocols

// MARK: - Import utilities from MasterFXUtilities

/// Enhanced overdrive effect specifically designed for master bus processing
public class MasterOverdriveEffect: FXProcessor, ObservableObject, @unchecked Sendable {
    
    // MARK: - Parameters
    
    /// Drive amount (0.0 to 10.0)
    public var driveAmount: Float = 1.0 {
        didSet {
            driveAmount = max(0.0, min(10.0, driveAmount))
        }
    }
    
    /// Saturation type
    public var saturationType: SaturationType = .tube {
        didSet {
            updateSaturationCurve()
        }
    }
    
    /// High-frequency emphasis (0.0 to 1.0)
    public var highFreqEmphasis: Float = 0.3 {
        didSet {
            highFreqEmphasis = max(0.0, min(1.0, highFreqEmphasis))
            updatePreEmphasisFilter()
        }
    }
    
    /// Low-frequency rolloff (0.0 to 1.0)
    public var lowFreqRolloff: Float = 0.2 {
        didSet {
            lowFreqRolloff = max(0.0, min(1.0, lowFreqRolloff))
            updateHighPassFilter()
        }
    }
    
    /// Output level compensation (-20 to 20 dB)
    public var outputLevel: Float = 0.0 {
        didSet {
            outputLevel = max(-20.0, min(20.0, outputLevel))
        }
    }
    
    /// Stereo width enhancement (0.0 to 2.0)
    public override var stereoWidth: Float {
        get { super.stereoWidth }
        set {
            super.stereoWidth = max(0.0, min(2.0, newValue))
        }
    }

    private var _stereoWidth: Float = 1.0 {
        didSet {
            _stereoWidth = max(0.0, min(2.0, _stereoWidth))
        }
    }
    
    /// Harmonic enhancement (0.0 to 1.0)
    public var harmonicEnhancement: Float = 0.0 {
        didSet {
            harmonicEnhancement = max(0.0, min(1.0, harmonicEnhancement))
        }
    }
    
    // MARK: - Internal State
    
    private var sampleRate: Double = 44100.0
    private var preEmphasisFilter: BiquadFilter
    private var deEmphasisFilter: BiquadFilter
    private var highPassFilter: BiquadFilter
    private var dcBlocker: DCBlocker
    private var harmonicGenerator: HarmonicGenerator
    
    // Saturation curve parameters
    private var saturationCurve: SaturationCurve = SaturationCurve()
    
    // Metering
    @Published public var inputLevel: Float = 0.0
    @Published public var outputLevelMeter: Float = 0.0
    @Published public var harmonicContent: Float = 0.0
    
    // MARK: - Saturation Types
    
    public enum SaturationType: String, CaseIterable, Codable, Sendable {
        case tube = "Tube"
        case tape = "Tape"
        case transistor = "Transistor"
        case digital = "Digital"
        case vintage = "Vintage"
        
        fileprivate var curve: SaturationCurveType {
            switch self {
            case .tube: return .tube
            case .tape: return .tape
            case .transistor: return .transistor
            case .digital: return .digital
            case .vintage: return .vintage
            }
        }
    }
    
    // MARK: - Initialization
    
    public init() {
        self.preEmphasisFilter = BiquadFilter(sampleRate: sampleRate)
        self.deEmphasisFilter = BiquadFilter(sampleRate: sampleRate)
        self.highPassFilter = BiquadFilter(sampleRate: sampleRate)
        self.dcBlocker = DCBlocker(sampleRate: sampleRate)
        self.harmonicGenerator = HarmonicGenerator(sampleRate: sampleRate)

        super.init(name: "Master Overdrive")

        self.effectType = .overdrive
        
        setupFilters()
        updateSaturationCurve()
    }
    
    // MARK: - Audio Processing
    
    public override func process(input: MachineProtocols.AudioBuffer) -> MachineProtocols.AudioBuffer {
        guard !isBypassed && isEnabled else {
            return input
        }
        
        var output = input
        
        if input.channelCount == 2 {
            processStereoOverdrive(&output)
        } else {
            processMonoOverdrive(&output)
        }
        
        // Update meters
        updateMeters(input: input, output: output)
        
        return output
    }
    
    private func processStereoOverdrive(_ buffer: inout MachineProtocols.AudioBuffer) {
        let frameCount = buffer.frameCount
        
        for frame in 0..<frameCount {
            let leftIndex = frame * 2
            let rightIndex = frame * 2 + 1
            
            guard leftIndex < buffer.samples.count && rightIndex < buffer.samples.count else { break }
            
            var leftSample = buffer.samples[leftIndex]
            var rightSample = buffer.samples[rightIndex]
            
            // Apply high-pass filter for low-frequency rolloff
            leftSample = highPassFilter.process(leftSample, channel: 0)
            rightSample = highPassFilter.process(rightSample, channel: 1)
            
            // Apply pre-emphasis for high-frequency enhancement
            leftSample = preEmphasisFilter.process(leftSample, channel: 0)
            rightSample = preEmphasisFilter.process(rightSample, channel: 1)
            
            // Apply drive
            leftSample *= driveAmount
            rightSample *= driveAmount
            
            // Apply saturation
            leftSample = saturationCurve.process(leftSample)
            rightSample = saturationCurve.process(rightSample)
            
            // Apply harmonic enhancement
            if harmonicEnhancement > 0.0 {
                let leftHarmonics = harmonicGenerator.process(leftSample, channel: 0)
                let rightHarmonics = harmonicGenerator.process(rightSample, channel: 1)
                
                leftSample += leftHarmonics * harmonicEnhancement
                rightSample += rightHarmonics * harmonicEnhancement
            }
            
            // Apply de-emphasis to balance pre-emphasis
            leftSample = deEmphasisFilter.process(leftSample, channel: 0)
            rightSample = deEmphasisFilter.process(rightSample, channel: 1)
            
            // Apply DC blocking
            leftSample = dcBlocker.process(leftSample, channel: 0)
            rightSample = dcBlocker.process(rightSample, channel: 1)
            
            // Apply stereo width enhancement
            if stereoWidth != 1.0 {
                let mid = (leftSample + rightSample) * 0.5
                let side = (leftSample - rightSample) * 0.5 * stereoWidth
                
                leftSample = mid + side
                rightSample = mid - side
            }
            
            // Apply output level compensation
            let outputGain = pow(10.0, outputLevel / 20.0)
            leftSample *= outputGain
            rightSample *= outputGain
            
            buffer.samples[leftIndex] = leftSample
            buffer.samples[rightIndex] = rightSample
        }
    }
    
    private func processMonoOverdrive(_ buffer: inout MachineProtocols.AudioBuffer) {
        for i in 0..<buffer.samples.count {
            var sample = buffer.samples[i]
            
            // Apply high-pass filter
            sample = highPassFilter.process(sample, channel: 0)
            
            // Apply pre-emphasis
            sample = preEmphasisFilter.process(sample, channel: 0)
            
            // Apply drive
            sample *= driveAmount
            
            // Apply saturation
            sample = saturationCurve.process(sample)
            
            // Apply harmonic enhancement
            if harmonicEnhancement > 0.0 {
                let harmonics = harmonicGenerator.process(sample, channel: 0)
                sample += harmonics * harmonicEnhancement
            }
            
            // Apply de-emphasis
            sample = deEmphasisFilter.process(sample, channel: 0)
            
            // Apply DC blocking
            sample = dcBlocker.process(sample, channel: 0)
            
            // Apply output level compensation
            let outputGain = pow(10.0, outputLevel / 20.0)
            sample *= outputGain
            
            buffer.samples[i] = sample
        }
    }
    
    // MARK: - Filter Setup and Updates
    
    private func setupFilters() {
        updatePreEmphasisFilter()
        updateHighPassFilter()
        setupDeEmphasisFilter()
    }
    
    private func updatePreEmphasisFilter() {
        // High-shelf filter for pre-emphasis
        let frequency = 3000.0 + highFreqEmphasis * 7000.0 // 3kHz to 10kHz
        let gain = highFreqEmphasis * 6.0 // Up to 6dB boost
        
        preEmphasisFilter.setHighShelf(
            frequency: Float(frequency),
            gain: gain,
            q: 0.7
        )
    }
    
    private func setupDeEmphasisFilter() {
        // Complementary filter to pre-emphasis
        let frequency = 3000.0 + highFreqEmphasis * 7000.0
        let gain = -highFreqEmphasis * 3.0 // Partial compensation
        
        deEmphasisFilter.setHighShelf(
            frequency: Float(frequency),
            gain: gain,
            q: 0.7
        )
    }
    
    private func updateHighPassFilter() {
        // High-pass filter for low-frequency rolloff
        let frequency = 20.0 + lowFreqRolloff * 200.0 // 20Hz to 220Hz
        
        highPassFilter.setHighPass(
            frequency: Float(frequency),
            q: 0.7
        )
    }
    
    private func updateSaturationCurve() {
        saturationCurve.setCurveType(saturationType.curve)
    }
    
    // MARK: - Metering
    
    private func updateMeters(input: MachineProtocols.AudioBuffer, output: MachineProtocols.AudioBuffer) {
        // Calculate input level
        var inputPeak: Float = 0.0
        for sample in input.samples {
            inputPeak = max(inputPeak, abs(sample))
        }
        self.inputLevel = inputPeak
        
        // Calculate output level
        var outputPeak: Float = 0.0
        for sample in output.samples {
            outputPeak = max(outputPeak, abs(sample))
        }
        self.outputLevel = outputPeak
        
        // Estimate harmonic content (simplified)
        self.harmonicContent = harmonicGenerator.getHarmonicLevel()
    }
    
    // MARK: - State Management
    
    public override func resetEffectState() {
        super.resetEffectState()
        preEmphasisFilter.reset()
        deEmphasisFilter.reset()
        highPassFilter.reset()
        dcBlocker.reset()
        harmonicGenerator.reset()
        saturationCurve.reset()
    }
    
    // MARK: - Sample Rate
    
    public func setSampleRate(_ sampleRate: Double) {
        self.sampleRate = sampleRate
        setupFilters()
    }
    
    // MARK: - Presets
    
    public enum OverdrivePreset {
        case clean
        case warm
        case vintage
        case modern
        case aggressive
        case subtle
        
        var parameters: (drive: Float, saturation: SaturationType, highEmph: Float, lowRoll: Float, output: Float, stereoWidth: Float, harmonics: Float) {
            switch self {
            case .clean:
                return (1.0, .tube, 0.1, 0.1, 0.0, 1.0, 0.0)
            case .warm:
                return (2.0, .tube, 0.3, 0.2, -2.0, 1.1, 0.2)
            case .vintage:
                return (3.0, .tape, 0.4, 0.3, -4.0, 1.0, 0.3)
            case .modern:
                return (2.5, .transistor, 0.5, 0.1, -3.0, 1.2, 0.1)
            case .aggressive:
                return (5.0, .digital, 0.6, 0.2, -6.0, 1.3, 0.4)
            case .subtle:
                return (1.5, .tube, 0.2, 0.15, -1.0, 1.05, 0.1)
            }
        }
    }
    
    /// Apply an overdrive preset
    public func applyPreset(_ preset: OverdrivePreset) {
        let params = preset.parameters
        driveAmount = params.drive
        saturationType = params.saturation
        highFreqEmphasis = params.highEmph
        lowFreqRolloff = params.lowRoll
        outputLevel = params.output
        stereoWidth = params.stereoWidth
        harmonicEnhancement = params.harmonics
    }
}

// MARK: - Supporting Classes

/// Saturation curve processor
private class SaturationCurve {
    private var curveType: SaturationCurveType = .tube
    
    func process(_ input: Float) -> Float {
        let clampedInput = max(-3.0, min(3.0, input)) // Prevent extreme values
        
        switch curveType {
        case .tube:
            return tubeSaturation(clampedInput)
        case .tape:
            return tapeSaturation(clampedInput)
        case .transistor:
            return transistorSaturation(clampedInput)
        case .digital:
            return digitalSaturation(clampedInput)
        case .vintage:
            return vintageSaturation(clampedInput)
        }
    }
    
    private func tubeSaturation(_ input: Float) -> Float {
        // Asymmetric tube-style saturation
        if input >= 0 {
            return tanh(input * 0.7)
        } else {
            return tanh(input * 1.2)
        }
    }
    
    private func tapeSaturation(_ input: Float) -> Float {
        // Tape-style compression and saturation
        let compressed = input / (1.0 + abs(input) * 0.3)
        return tanh(compressed * 1.5)
    }
    
    private func transistorSaturation(_ input: Float) -> Float {
        // Hard clipping with soft knee
        let threshold: Float = 0.8
        if abs(input) < threshold {
            return input
        } else {
            let sign = input >= 0 ? 1.0 : -1.0
            let excess = abs(input) - threshold
            return Float(sign * (Double(threshold) + Double(excess) * 0.2))
        }
    }
    
    private func digitalSaturation(_ input: Float) -> Float {
        // Hard digital clipping
        return max(-1.0, min(1.0, input))
    }
    
    private func vintageSaturation(_ input: Float) -> Float {
        // Vintage-style warm saturation
        return input - (input * input * input) / 3.0
    }
    
    func setCurveType(_ type: SaturationCurveType) {
        curveType = type
    }
    
    func reset() {
        // No state to reset for stateless saturation
    }
}

/// Saturation curve types
private enum SaturationCurveType {
    case tube, tape, transistor, digital, vintage
}


