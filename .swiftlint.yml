disabled_rules:
  - trailing_whitespace
  - line_length

opt_in_rules:
  - empty_count
  - empty_string
  - force_unwrapping
  - implicitly_unwrapped_optional
  - overridden_super_call
  - redundant_nil_coalescing
  - switch_case_on_newline
  - unneeded_parentheses_in_closure_argument
  - vertical_whitespace_closing_braces

included:
  - Sources
  - Tests

excluded:
  - .build
  - .swiftpm
  - DigitonePad.xcodeproj
  - Resources

line_length:
  warning: 120
  error: 150

function_body_length:
  warning: 50
  error: 100

type_body_length:
  warning: 300
  error: 500

file_length:
  warning: 500
  error: 1000

cyclomatic_complexity:
  warning: 10
  error: 20

nesting:
  type_level:
    warning: 2
    error: 3
  statement_level:
    warning: 5
    error: 10

identifier_name:
  min_length:
    warning: 2
    error: 1
  max_length:
    warning: 40
    error: 50
  excluded:
    - id
    - x
    - y
    - z

custom_rules:
  no_print:
    name: "No Print Statements"
    regex: "print\\("
    message: "Use proper logging instead of print statements"
    severity: warning 