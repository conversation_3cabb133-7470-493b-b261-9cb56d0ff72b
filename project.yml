name: DigitonePad
options:
  bundleIdPrefix: com.digitonepad
  deploymentTarget:
    iOS: "16.0"
  developmentLanguage: en
  createIntermediateGroups: true
  groupSortPosition: top
  generateEmptyDirectories: true

settings:
  base:
    PRODUCT_NAME: DigitonePad
    MARKETING_VERSION: "1.0.0"
    CURRENT_PROJECT_VERSION: "1"
    SWIFT_VERSION: "5.9"
    IPHONEOS_DEPLOYMENT_TARGET: "16.0"
    TARGETED_DEVICE_FAMILY: "1,2"
    SUPPORTS_MACCATALYST: false
    ENABLE_BITCODE: false
    SWIFT_STRICT_CONCURRENCY: complete
    # Reduce code signing verbosity for simulator builds
    CODE_SIGN_IDENTITY[sdk=iphonesimulator*]: ""
    CODE_SIGN_STYLE: Automatic
    DEVELOPMENT_TEAM: ""

# External packages can be added here when needed
# packages:
#   SomePackage:
#     url: https://github.com/example/package
#     from: "1.0.0"

targets:
  DigitonePad:
    type: application
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/DigitonePad
        excludes:
          - "**/*.md"
    resources:
      - path: Resources
        optional: true
    dependencies:
      - target: AppShell
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.app
        INFOPLIST_FILE: Resources/Info.plist
        ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
        ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME: AccentColor
        ENABLE_PREVIEWS: true
        DEVELOPMENT_ASSET_PATHS: "\"Resources/Preview Content\""
    info:
      path: Resources/Info.plist
      properties:
        CFBundleDisplayName: DigitonePad
        CFBundleShortVersionString: "$(MARKETING_VERSION)"
        CFBundleVersion: "$(CURRENT_PROJECT_VERSION)"
        UILaunchStoryboardName: LaunchScreen
        UISupportedInterfaceOrientations:
          - UIInterfaceOrientationPortrait
          - UIInterfaceOrientationLandscapeLeft
          - UIInterfaceOrientationLandscapeRight
        UISupportedInterfaceOrientations~ipad:
          - UIInterfaceOrientationPortrait
          - UIInterfaceOrientationPortraitUpsideDown
          - UIInterfaceOrientationLandscapeLeft
          - UIInterfaceOrientationLandscapeRight
        NSMicrophoneUsageDescription: "DigitonePad needs microphone access for audio input and recording."
        NSBluetoothAlwaysUsageDescription: "DigitonePad uses Bluetooth for MIDI device connectivity."
        UIBackgroundModes:
          - audio
        AVAudioSessionCategory: AVAudioSessionCategoryPlayAndRecord
        AVAudioSessionCategoryOptions:
          - AVAudioSessionCategoryOptionMixWithOthers
          - AVAudioSessionCategoryOptionAllowBluetooth

  # Framework targets for each module
  MachineProtocols:
    type: framework
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/MachineProtocols
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.machineprotocols
        PRODUCT_NAME: MachineProtocols
        GENERATE_INFOPLIST_FILE: true

  DataModel:
    type: framework
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/DataModel
        excludes:
          - "Resources/**"
          - "Documentation.docc/**"
    resources:
      - path: Sources/DataModel/Resources
        optional: true
    dependencies:
      - target: MachineProtocols
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.datamodel
        PRODUCT_NAME: DataModel
        GENERATE_INFOPLIST_FILE: true

  DataLayer:
    type: framework
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/DataLayer
        excludes:
          - "Resources/**"
          - "Documentation.docc/**"
    resources:
      - path: Sources/DataLayer/Resources
        optional: true
    dependencies:
      - target: MachineProtocols
      - target: DataModel
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.datalayer
        PRODUCT_NAME: DataLayer
        GENERATE_INFOPLIST_FILE: true

  AudioEngine:
    type: framework
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/AudioEngine
        excludes:
          - "README.md"
          - "Documentation/**"
    dependencies:
      - target: MachineProtocols
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.audioengine
        PRODUCT_NAME: AudioEngine
        GENERATE_INFOPLIST_FILE: true

  SequencerModule:
    type: framework
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/SequencerModule
    dependencies:
      - target: MachineProtocols
      - target: DataLayer
      - target: DataModel
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.sequencermodule
        PRODUCT_NAME: SequencerModule
        GENERATE_INFOPLIST_FILE: true

  VoiceModule:
    type: framework
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/VoiceModule
    dependencies:
      - target: MachineProtocols
      - target: AudioEngine
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.voicemodule
        PRODUCT_NAME: VoiceModule
        GENERATE_INFOPLIST_FILE: true

  FilterModule:
    type: framework
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/FilterModule
    dependencies:
      - target: MachineProtocols
      - target: AudioEngine
      - target: VoiceModule
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.filtermodule
        PRODUCT_NAME: FilterModule
        GENERATE_INFOPLIST_FILE: true

  FilterMachine:
    type: framework
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/FilterMachine
    dependencies:
      - target: MachineProtocols
      - target: AudioEngine
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.filtermachine
        PRODUCT_NAME: FilterMachine
        GENERATE_INFOPLIST_FILE: true

  FXModule:
    type: framework
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/FXModule
    dependencies:
      - target: MachineProtocols
      - target: AudioEngine
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.fxmodule
        PRODUCT_NAME: FXModule
        GENERATE_INFOPLIST_FILE: true

  MIDIModule:
    type: framework
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/MIDIModule
    dependencies:
      - target: MachineProtocols
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.midimodule
        PRODUCT_NAME: MIDIModule
        GENERATE_INFOPLIST_FILE: true

  UIComponents:
    type: framework
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/UIComponents
        excludes:
          - "KeyCombo/KeyComboSystemDesign.md"
    dependencies:
      - target: MachineProtocols
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.uicomponents
        PRODUCT_NAME: UIComponents
        GENERATE_INFOPLIST_FILE: true

  AppShell:
    type: framework
    platform: iOS
    deploymentTarget: "16.0"
    sources:
      - path: Sources/AppShell
    dependencies:
      - target: DataLayer
      - target: DataModel
      - target: AudioEngine
      - target: SequencerModule
      - target: VoiceModule
      - target: FilterModule
      - target: FilterMachine
      - target: FXModule
      - target: MIDIModule
      - target: UIComponents
      - target: MachineProtocols
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.appshell
        PRODUCT_NAME: AppShell
        GENERATE_INFOPLIST_FILE: true

  # Test targets
  DigitonePadTests:
    type: bundle.unit-test
    platform: iOS
    sources:
      - path: Tests
        excludes:
          - "**/*.md"
    dependencies:
      - target: MachineProtocols
      - target: DataModel
      - target: DataLayer
      - target: AudioEngine
      - target: SequencerModule
      - target: VoiceModule
      - target: FilterModule
      - target: FilterMachine
      - target: FXModule
      - target: MIDIModule
      - target: UIComponents
      - target: AppShell
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.digitonepad.tests
        GENERATE_INFOPLIST_FILE: YES
        PRODUCT_MODULE_NAME: DigitonePadTests
        TEST_HOST: ""
        # Reduce code signing verbosity for tests
        CODE_SIGN_IDENTITY[sdk=iphonesimulator*]: ""
        CODE_SIGN_STYLE: Automatic

schemes:
  DigitonePad:
    build:
      targets:
        DigitonePad: all
    run:
      config: Debug
    test:
      config: Debug
      targets:
        - name: DigitonePadTests
          parallelizable: true
    profile:
      config: Release
    analyze:
      config: Debug
    archive:
      config: Release

  DigitonePadTests:
    build:
      targets:
        DigitonePadTests: all
    test:
      config: Debug
      targets:
        - DigitonePadTests